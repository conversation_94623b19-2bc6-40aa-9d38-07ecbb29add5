package cn.edu.boyapeihu.entity;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.ForeignKey;
import androidx.room.Ignore;

@Entity(tableName = "payment",
        foreignKeys = {
            @ForeignKey(entity = User.class, parentColumns = "id", childColumns = "patient_id"),
            @ForeignKey(entity = User.class, parentColumns = "id", childColumns = "attendant_id")
        })
public class Payment {
    @PrimaryKey(autoGenerate = true)
    private int id;
    
    private int patient_id;
    private int attendant_id;
    private double amount;
    private String payment_time;  // 支付时间
    private String payment_type;  // 支付类型：现金、微信、支付宝等
    private String status;        // 支付状态：pending、completed、failed
    private String description;   // 支付描述
    
    // 构造函数
    public Payment() {}
    
    @Ignore
    public Payment(int patient_id, int attendant_id, double amount, String payment_time, String payment_type) {
        this.patient_id = patient_id;
        this.attendant_id = attendant_id;
        this.amount = amount;
        this.payment_time = payment_time;
        this.payment_type = payment_type;
    }
    
    @Ignore
    public Payment(int patient_id, int attendant_id, double amount, String payment_time, String payment_type, String description) {
        this.patient_id = patient_id;
        this.attendant_id = attendant_id;
        this.amount = amount;
        this.payment_time = payment_time;
        this.payment_type = payment_type;
        this.description = description;
    }
    
    // Getter和Setter方法
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public int getPatient_id() {
        return patient_id;
    }
    
    public void setPatient_id(int patient_id) {
        this.patient_id = patient_id;
    }
    
    public int getAttendant_id() {
        return attendant_id;
    }
    
    public void setAttendant_id(int attendant_id) {
        this.attendant_id = attendant_id;
    }
    
    public double getAmount() {
        return amount;
    }
    
    public void setAmount(double amount) {
        this.amount = amount;
    }
    
    public String getPayment_time() {
        return payment_time;
    }
    
    public void setPayment_time(String payment_time) {
        this.payment_time = payment_time;
    }
    
    public String getPayment_type() {
        return payment_type;
    }
    
    public void setPayment_type(String payment_type) {
        this.payment_type = payment_type;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    // 向后兼容的getter方法
    public String getTime() {
        return payment_time;
    }
    
    public void setTime(String time) {
        this.payment_time = time;
    }
    
    public String getMethod() {
        return payment_type;
    }
    
    public void setMethod(String method) {
        this.payment_type = method;
    }
    
    public String getRemark() {
        return description;
    }
    
    public void setRemark(String remark) {
        this.description = remark;
    }
    
    // 支付方式常量
    public static final String METHOD_CASH = "cash";
    public static final String METHOD_WECHAT = "wechat";
    public static final String METHOD_ALIPAY = "alipay";
    
    // 状态常量
    public static final String STATUS_PENDING = "pending";
    public static final String STATUS_COMPLETED = "completed";
    public static final String STATUS_FAILED = "failed";
} 