<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/backgroundColor">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        android:theme="@style/ThemeOverlay.AppCompat.ActionBar"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 人员选择 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/cardBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="选择评价对象"
                        android:textSize="16sp"
                        android:textColor="@color/textColorPrimary"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_person"
                        android:drawablePadding="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="陪护人员"
                        android:textSize="14sp"
                        android:textColor="@color/textColorSecondary"
                        android:layout_marginBottom="4dp" />

                    <Spinner
                        android:id="@+id/spinner_attendant"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="对应患者"
                        android:textSize="14sp"
                        android:textColor="@color/textColorSecondary"
                        android:layout_marginBottom="4dp" />

                    <Spinner
                        android:id="@+id/spinner_patient"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 评价时间 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/cardBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="评价时间"
                        android:textSize="16sp"
                        android:textColor="@color/textColorPrimary"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_time"
                        android:drawablePadding="8dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <EditText
                            android:id="@+id/et_evaluation_date"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            android:hint="选择日期"
                            android:focusable="false"
                            android:clickable="true"
                            android:background="@drawable/bg_status_pill"
                            android:padding="12dp"
                            android:textSize="14sp" />

                        <EditText
                            android:id="@+id/et_evaluation_time"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            android:hint="选择时间"
                            android:focusable="false"
                            android:clickable="true"
                            android:background="@drawable/bg_status_pill"
                            android:padding="12dp"
                            android:textSize="14sp" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 质量评分 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/cardBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="质量评分"
                        android:textSize="16sp"
                        android:textColor="@color/textColorPrimary"
                        android:textStyle="bold"
                        android:layout_marginBottom="16dp"
                        android:drawableStart="@drawable/ic_quality"
                        android:drawablePadding="8dp" />

                    <!-- 服务态度 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="服务态度"
                            android:textSize="14sp"
                            android:textColor="@color/textColorPrimary"
                            android:layout_marginBottom="4dp" />

                        <RatingBar
                            android:id="@+id/rb_service_attitude"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:numStars="5"
                            android:stepSize="1"
                            android:rating="0" />

                    </LinearLayout>

                    <!-- 专业技能 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="专业技能"
                            android:textSize="14sp"
                            android:textColor="@color/textColorPrimary"
                            android:layout_marginBottom="4dp" />

                        <RatingBar
                            android:id="@+id/rb_professional_skill"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:numStars="5"
                            android:stepSize="1"
                            android:rating="0" />

                    </LinearLayout>

                    <!-- 沟通能力 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="沟通能力"
                            android:textSize="14sp"
                            android:textColor="@color/textColorPrimary"
                            android:layout_marginBottom="4dp" />

                        <RatingBar
                            android:id="@+id/rb_communication"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:numStars="5"
                            android:stepSize="1"
                            android:rating="0" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 评价意见 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/cardBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="评价意见（可选）"
                        android:textSize="16sp"
                        android:textColor="@color/textColorPrimary"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_message"
                        android:drawablePadding="8dp" />

                    <EditText
                        android:id="@+id/et_comments"
                        android:layout_width="match_parent"
                        android:layout_height="120dp"
                        android:hint="请输入具体的评价意见和建议..."
                        android:gravity="top|start"
                        android:background="@drawable/bg_status_pill"
                        android:padding="12dp"
                        android:textSize="14sp"
                        android:maxLines="6" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 保存按钮 -->
            <Button
                android:id="@+id/btn_save"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="保存质量评价"
                android:textSize="16sp"
                android:textStyle="bold"
                android:background="@drawable/bg_status_pill"
                android:backgroundTint="?attr/colorPrimary"
                android:textColor="@android:color/white"
                android:padding="16dp"
                android:layout_marginTop="16dp" />

        </LinearLayout>

    </ScrollView>

</LinearLayout> 