<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/backgroundColor">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        android:theme="@style/ThemeOverlay.AppCompat.ActionBar"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 基本信息 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/cardBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="基本信息"
                        android:textSize="18sp"
                        android:textColor="@color/textColorPrimary"
                        android:textStyle="bold"
                        android:layout_marginBottom="16dp"
                        android:drawableStart="@drawable/ic_profile"
                        android:drawablePadding="8dp" />

                    <!-- 角色 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="80dp"
                            android:layout_height="wrap_content"
                            android:text="角色:"
                            android:textSize="14sp"
                            android:textColor="@color/textColorSecondary" />

                        <TextView
                            android:id="@+id/tv_role"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="医护人员"
                            android:textSize="14sp"
                            android:textColor="@color/textColorPrimary"
                            android:textStyle="bold" />

                    </LinearLayout>

                    <!-- 手机号 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="80dp"
                            android:layout_height="wrap_content"
                            android:text="手机号:"
                            android:textSize="14sp"
                            android:textColor="@color/textColorSecondary" />

                        <TextView
                            android:id="@+id/tv_phone"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="13800138000"
                            android:textSize="14sp"
                            android:textColor="@color/textColorPrimary" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 修改姓名 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/cardBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="修改姓名"
                        android:textSize="16sp"
                        android:textColor="@color/textColorPrimary"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_person"
                        android:drawablePadding="8dp" />

                    <EditText
                        android:id="@+id/et_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="请输入姓名"
                        android:background="@drawable/bg_status_pill"
                        android:padding="12dp"
                        android:textSize="14sp"
                        android:layout_marginBottom="12dp" />

                    <Button
                        android:id="@+id/btn_update_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="更新姓名"
                        android:textSize="14sp"
                        android:background="@drawable/bg_status_pill"
                        android:backgroundTint="?attr/colorPrimary"
                        android:textColor="@android:color/white"
                        android:padding="12dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 修改密码 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/cardBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="修改密码"
                        android:textSize="16sp"
                        android:textColor="@color/textColorPrimary"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_lock"
                        android:drawablePadding="8dp" />

                    <EditText
                        android:id="@+id/et_current_password"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="请输入当前密码"
                        android:inputType="textPassword"
                        android:background="@drawable/bg_status_pill"
                        android:padding="12dp"
                        android:textSize="14sp"
                        android:layout_marginBottom="8dp" />

                    <EditText
                        android:id="@+id/et_new_password"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="请输入新密码"
                        android:inputType="textPassword"
                        android:background="@drawable/bg_status_pill"
                        android:padding="12dp"
                        android:textSize="14sp"
                        android:layout_marginBottom="8dp" />

                    <EditText
                        android:id="@+id/et_confirm_password"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="请确认新密码"
                        android:inputType="textPassword"
                        android:background="@drawable/bg_status_pill"
                        android:padding="12dp"
                        android:textSize="14sp"
                        android:layout_marginBottom="12dp" />

                    <Button
                        android:id="@+id/btn_update_password"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="更新密码"
                        android:textSize="14sp"
                        android:background="@drawable/bg_status_pill"
                        android:backgroundTint="?attr/colorPrimary"
                        android:textColor="@android:color/white"
                        android:padding="12dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 退出登录 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/cardBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="账户管理"
                        android:textSize="16sp"
                        android:textColor="@color/textColorPrimary"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_logout"
                        android:drawablePadding="8dp" />

                    <Button
                        android:id="@+id/btn_logout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="退出登录"
                        android:textSize="14sp"
                        android:background="@drawable/bg_status_pill"
                        android:backgroundTint="@color/colorError"
                        android:textColor="@android:color/white"
                        android:padding="12dp"
                        android:drawableStart="@drawable/ic_logout"
                        android:drawablePadding="8dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </ScrollView>

</LinearLayout>