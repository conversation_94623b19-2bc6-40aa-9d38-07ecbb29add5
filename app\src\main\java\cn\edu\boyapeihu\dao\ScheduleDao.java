package cn.edu.boyapeihu.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import java.util.List;

import cn.edu.boyapeihu.entity.Schedule;

@Dao
public interface ScheduleDao {
    
    @Insert
    long insert(Schedule schedule);
    
    @Update
    void update(Schedule schedule);
    
    @Delete
    void delete(Schedule schedule);
    
    @Query("SELECT * FROM schedule WHERE id = :id")
    LiveData<Schedule> getScheduleById(int id);
    
    @Query("SELECT * FROM schedule WHERE attendant_id = :attendantId ORDER BY start_time DESC")
    LiveData<List<Schedule>> getSchedulesByAttendant(int attendantId);
    
    @Query("SELECT * FROM schedule WHERE patient_id = :patientId ORDER BY start_time DESC")
    LiveData<List<Schedule>> getSchedulesByPatient(int patientId);
    
    @Query("SELECT * FROM schedule WHERE nurse_id = :nurseId ORDER BY start_time DESC")
    LiveData<List<Schedule>> getSchedulesByNurse(int nurseId);
    
    @Query("SELECT * FROM schedule WHERE status = :status ORDER BY start_time DESC")
    LiveData<List<Schedule>> getSchedulesByStatus(String status);
    
    @Query("SELECT * FROM schedule ORDER BY start_time DESC")
    LiveData<List<Schedule>> getAllSchedules();
    
    @Query("SELECT * FROM schedule WHERE attendant_id = :attendantId AND status = :status ORDER BY start_time DESC")
    LiveData<List<Schedule>> getSchedulesByAttendantAndStatus(int attendantId, String status);
    
    @Query("SELECT * FROM schedule WHERE start_time >= :startDate AND end_time <= :endDate ORDER BY start_time DESC")
    LiveData<List<Schedule>> getSchedulesByDateRange(String startDate, String endDate);
    
    @Query("UPDATE schedule SET status = :status WHERE id = :id")
    void updateStatus(int id, String status);
    
    @Query("DELETE FROM schedule WHERE id = :id")
    void deleteById(int id);
} 