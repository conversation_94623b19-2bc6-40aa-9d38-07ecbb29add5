package cn.edu.boyapeihu.activity;

import android.content.Intent;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.floatingactionbutton.FloatingActionButton;

import java.util.List;

import cn.edu.boyapeihu.R;
import cn.edu.boyapeihu.adapter.MessageAdapter;
import cn.edu.boyapeihu.database.AppDatabase;
import cn.edu.boyapeihu.entity.Message;
import cn.edu.boyapeihu.utils.SessionManager;

public class MessageActivity extends AppCompatActivity {
    
    private RecyclerView recyclerView;
    private FloatingActionButton fabAdd;
    private MessageAdapter adapter;
    
    private AppDatabase database;
    private SessionManager sessionManager;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_message);
        
        initViews();
        initData();
        loadMessages();
    }
    
    private void initViews() {
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle("消息管理");
        
        recyclerView = findViewById(R.id.recycler_view);
        fabAdd = findViewById(R.id.fab_add);
        
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        adapter = new MessageAdapter(this);
        recyclerView.setAdapter(adapter);
        
        fabAdd.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                sendMessage();
            }
        });
    }
    
    private void initData() {
        database = AppDatabase.getDatabase(this);
        sessionManager = new SessionManager(this);
    }
    
    private void loadMessages() {
        int currentUserId = sessionManager.getCurrentUserId();
        
        // 加载当前用户相关的所有消息（发送的和接收的）
        database.messageDao().getMessagesByUser(currentUserId).observe(this, new Observer<List<Message>>() {
            @Override
            public void onChanged(List<Message> messages) {
                adapter.setMessages(messages);
                
                // 滚动到最新消息
                if (!messages.isEmpty()) {
                    recyclerView.scrollToPosition(messages.size() - 1);
                }
            }
        });
    }
    
    private void sendMessage() {
        Intent intent = new Intent(this, SendMessageActivity.class);
        startActivity(intent);
    }
    
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.message_menu, menu);
        return true;
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        
        if (id == android.R.id.home) {
            finish();
            return true;
        } else if (id == R.id.action_contacts) {
            // TODO: 显示联系人列表
            Toast.makeText(this, "联系人功能开发中", Toast.LENGTH_SHORT).show();
            return true;
        } else if (id == R.id.action_mark_read) {
            markAllAsRead();
            return true;
        }
        
        return super.onOptionsItemSelected(item);
    }
    
    private void markAllAsRead() {
        int currentUserId = sessionManager.getCurrentUserId();
        
        // 在后台线程执行数据库操作
        new Thread(new Runnable() {
            @Override
            public void run() {
                try {
                    database.messageDao().markAllAsRead(currentUserId);
                    
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            Toast.makeText(MessageActivity.this, "所有消息已标记为已读", Toast.LENGTH_SHORT).show();
                            loadMessages(); // 刷新列表
                        }
                    });
                } catch (Exception e) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            Toast.makeText(MessageActivity.this, "操作失败：" + e.getMessage(), Toast.LENGTH_SHORT).show();
                        }
                    });
                }
            }
        }).start();
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        // 返回时刷新列表
        loadMessages();
    }
} 