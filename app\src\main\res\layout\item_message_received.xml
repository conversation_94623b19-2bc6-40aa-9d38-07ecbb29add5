<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="8dp">

    <!-- 发送者信息 -->
    <TextView
        android:id="@+id/tv_sender_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="start"
        android:text="张三"
        android:textSize="12sp"
        android:textColor="@color/textColorSecondary"
        android:textStyle="bold"
        android:layout_marginBottom="4dp"
        android:layout_marginStart="16dp" />

    <!-- 接收者信息 -->
    <TextView
        android:id="@+id/tv_receiver_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="start"
        android:text=""
        android:textSize="10sp"
        android:textColor="@color/textColorSecondary"
        android:layout_marginBottom="2dp"
        android:layout_marginStart="16dp"
        android:visibility="gone" />

    <!-- 消息内容 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="start">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:maxWidth="280dp"
            android:orientation="vertical">

            <!-- 消息气泡 -->
            <androidx.cardview.widget.CardView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                app:cardCornerRadius="18dp"
                app:cardElevation="2dp"
                app:cardBackgroundColor="@color/cardBackground">

                <TextView
                    android:id="@+id/tv_message"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="这是一条接收到的消息内容"
                    android:textSize="14sp"
                    android:textColor="@color/textColorPrimary"
                    android:padding="12dp"
                    android:maxWidth="240dp" />

            </androidx.cardview.widget.CardView>

            <!-- 时间 -->
            <TextView
                android:id="@+id/tv_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:text="12:30"
                android:textSize="10sp"
                android:textColor="@color/textColorSecondary"
                android:layout_marginTop="4dp" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout> 