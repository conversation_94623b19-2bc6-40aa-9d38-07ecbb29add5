<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_light">

    <!-- 顶部标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:background="@color/white"
        android:elevation="4dp"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_arrow_back"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:padding="4dp" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="护理员详情"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/textColorPrimary"
            android:gravity="center"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="40dp" />

    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 护理员基本信息卡片 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- 头像和基本信息 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <RelativeLayout
                            android:layout_width="100dp"
                            android:layout_height="100dp">

                            <ImageView
                                android:id="@+id/iv_avatar"
                                android:layout_width="100dp"
                                android:layout_height="100dp"
                                android:src="@drawable/ic_person"
                                android:background="@drawable/bg_circle_gray"
                                android:padding="20dp"
                                android:scaleType="centerInside" />

                            <TextView
                                android:id="@+id/tv_level"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="高级"
                                android:textSize="12sp"
                                android:textColor="@color/white"
                                android:background="@drawable/bg_blue_badge"
                                android:paddingStart="8dp"
                                android:paddingEnd="8dp"
                                android:paddingTop="4dp"
                                android:paddingBottom="4dp"
                                android:layout_alignParentEnd="true"
                                android:layout_alignParentTop="true" />

                        </RelativeLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:layout_marginStart="20dp">

                            <!-- 姓名和状态 -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical">

                                <TextView
                                    android:id="@+id/tv_name"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:text="何春花"
                                    android:textSize="20sp"
                                    android:textStyle="bold"
                                    android:textColor="@color/textColorPrimary" />

                                <TextView
                                    android:id="@+id/tv_status"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="空闲"
                                    android:textSize="12sp"
                                    android:textColor="@color/green"
                                    android:background="@drawable/bg_status_available"
                                    android:paddingStart="8dp"
                                    android:paddingEnd="8dp"
                                    android:paddingTop="4dp"
                                    android:paddingBottom="4dp" />

                            </LinearLayout>

                            <!-- 职称 -->
                            <TextView
                                android:id="@+id/tv_title2"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="高级护工"
                                android:textSize="16sp"
                                android:textColor="@color/textColorSecondary"
                                android:layout_marginTop="6dp" />

                            <!-- 工作年限 -->
                            <TextView
                                android:id="@+id/tv_work_years"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="8年"
                                android:textSize="14sp"
                                android:textColor="@color/textColorSecondary"
                                android:layout_marginTop="4dp" />

                            <!-- 评分 -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical"
                                android:layout_marginTop="8dp">

                                <RatingBar
                                    android:id="@+id/rating_bar"
                                    style="?android:attr/ratingBarStyleSmall"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:numStars="5"
                                    android:rating="4.8"
                                    android:stepSize="0.1"
                                    android:isIndicator="true" />

                                <TextView
                                    android:id="@+id/tv_rating"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="4.8"
                                    android:textSize="14sp"
                                    android:textColor="@color/textColorPrimary"
                                    android:layout_marginStart="8dp" />

                                <TextView
                                    android:id="@+id/tv_review_count"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="(128条评价)"
                                    android:textSize="12sp"
                                    android:textColor="@color/textColorSecondary"
                                    android:layout_marginStart="4dp" />

                            </LinearLayout>

                        </LinearLayout>

                    </LinearLayout>

                    <!-- 个人格言 -->
                    <TextView
                        android:id="@+id/tv_quote"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="温柔有耐心，用心照顾每一位患者"
                        android:textSize="14sp"
                        android:textColor="@color/textColorOrange"
                        android:textStyle="italic"
                        android:gravity="center"
                        android:layout_marginTop="16dp"
                        android:background="@drawable/bg_quote"
                        android:padding="12dp" />

                    <!-- 联系按钮 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginTop="16dp">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:background="?attr/selectableItemBackground"
                            android:padding="12dp">

                            <ImageView
                                android:id="@+id/iv_call"
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:src="@drawable/ic_phone"
                                android:background="@drawable/bg_circle_blue"
                                android:padding="8dp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="电话"
                                android:textSize="12sp"
                                android:textColor="@color/textColorSecondary"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:background="?attr/selectableItemBackground"
                            android:padding="12dp">

                            <ImageView
                                android:id="@+id/iv_message"
                                android:layout_width="32dp"
                                android:layout_height="32dp"
                                android:src="@drawable/ic_message"
                                android:background="@drawable/bg_circle_green"
                                android:padding="8dp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="消息"
                                android:textSize="12sp"
                                android:textColor="@color/textColorSecondary"
                                android:layout_marginTop="4dp" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 详细信息卡片 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="详细信息"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/textColorPrimary"
                        android:layout_marginBottom="16dp" />

                    <!-- 个人简介 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="个人简介"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="@color/textColorSecondary"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:id="@+id/tv_description"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="拥有丰富的护理经验，专业技能扎实，对待患者温柔耐心，深受患者和家属的信赖。"
                            android:textSize="14sp"
                            android:textColor="@color/textColorPrimary"
                            android:lineSpacingExtra="4dp" />

                    </LinearLayout>

                    <!-- 工作经验 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="工作经验"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="@color/textColorSecondary"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:id="@+id/tv_experience"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="曾在三甲医院工作5年，具有丰富的临床护理经验，擅长老年护理和康复护理。"
                            android:textSize="14sp"
                            android:textColor="@color/textColorPrimary"
                            android:lineSpacingExtra="4dp" />

                    </LinearLayout>

                    <!-- 专业特长 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="专业特长"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="@color/textColorSecondary"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:id="@+id/tv_specialties"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="老年护理、康复护理、基础护理、生活照料"
                            android:textSize="14sp"
                            android:textColor="@color/textColorPrimary"
                            android:lineSpacingExtra="4dp" />

                    </LinearLayout>

                    <!-- 教育背景 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="教育背景"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="@color/textColorSecondary"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:id="@+id/tv_education"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="护理专业大专毕业"
                            android:textSize="14sp"
                            android:textColor="@color/textColorPrimary"
                            android:lineSpacingExtra="4dp" />

                    </LinearLayout>

                    <!-- 资格证书 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="资格证书"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="@color/textColorSecondary"
                            android:layout_marginBottom="8dp" />

                        <TextView
                            android:id="@+id/tv_certifications"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="护士执业证书、高级护工证书、急救证书"
                            android:textSize="14sp"
                            android:textColor="@color/textColorPrimary"
                            android:lineSpacingExtra="4dp" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 联系信息卡片 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="联系信息"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/textColorPrimary"
                        android:layout_marginBottom="16dp" />

                    <!-- 电话 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="12dp">

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_phone"
                            android:layout_marginEnd="12dp" />

                        <TextView
                            android:id="@+id/tv_phone"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="138****8888"
                            android:textSize="14sp"
                            android:textColor="@color/textColorPrimary" />

                    </LinearLayout>

                    <!-- 邮箱 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <ImageView
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:src="@drawable/ic_email"
                            android:layout_marginEnd="12dp" />

                        <TextView
                            android:id="@+id/tv_email"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="<EMAIL>"
                            android:textSize="14sp"
                            android:textColor="@color/textColorPrimary" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </ScrollView>

</LinearLayout>
