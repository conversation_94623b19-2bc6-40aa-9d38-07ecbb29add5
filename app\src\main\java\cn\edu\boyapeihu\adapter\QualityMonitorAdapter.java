package cn.edu.boyapeihu.adapter;

import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import cn.edu.boyapeihu.R;
import cn.edu.boyapeihu.database.AppDatabase;
import cn.edu.boyapeihu.entity.QualityMonitor;
import cn.edu.boyapeihu.entity.User;
import cn.edu.boyapeihu.utils.DateTimeUtils;

public class QualityMonitorAdapter extends RecyclerView.Adapter<QualityMonitorAdapter.QualityMonitorViewHolder> {
    
    private Context context;
    private List<QualityMonitor> qualityMonitors;
    private AppDatabase database;
    private ExecutorService executor;
    
    public QualityMonitorAdapter(Context context) {
        this.context = context;
        this.qualityMonitors = new ArrayList<>();
        this.database = AppDatabase.getDatabase(context);
        this.executor = Executors.newSingleThreadExecutor();
    }
    
    @NonNull
    @Override
    public QualityMonitorViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_quality_monitor, parent, false);
        return new QualityMonitorViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull QualityMonitorViewHolder holder, int position) {
        QualityMonitor qualityMonitor = qualityMonitors.get(position);
        holder.bind(qualityMonitor);
    }
    
    @Override
    public int getItemCount() {
        return qualityMonitors.size();
    }
    
    public void setQualityMonitors(List<QualityMonitor> qualityMonitors) {
        this.qualityMonitors = qualityMonitors;
        notifyDataSetChanged();
    }
    
    class QualityMonitorViewHolder extends RecyclerView.ViewHolder {
        
        private TextView tvAttendantName, tvPatientName, tvNurseName, tvEvaluationTime, tvQualityScore, tvServiceAttitude, tvProfessionalSkill, tvCommunication, tvComments;
        
        public QualityMonitorViewHolder(@NonNull View itemView) {
            super(itemView);
            
            tvAttendantName = itemView.findViewById(R.id.tv_attendant_name);
            tvPatientName = itemView.findViewById(R.id.tv_patient_name);
            tvNurseName = itemView.findViewById(R.id.tv_nurse_name);
            tvEvaluationTime = itemView.findViewById(R.id.tv_evaluation_time);
            tvQualityScore = itemView.findViewById(R.id.tv_quality_score);
            tvServiceAttitude = itemView.findViewById(R.id.tv_service_attitude);
            tvProfessionalSkill = itemView.findViewById(R.id.tv_professional_skill);
            tvCommunication = itemView.findViewById(R.id.tv_communication);
            tvComments = itemView.findViewById(R.id.tv_comments);
        }
        
        public void bind(QualityMonitor qualityMonitor) {
            // 设置评价时间
            tvEvaluationTime.setText(DateTimeUtils.formatDateTime(qualityMonitor.getEvaluation_time()));
            
            // 设置评分
            int qualityScore = qualityMonitor.getQuality_score();
            tvQualityScore.setText("综合评分: " + qualityScore + "/5");
            
            // 根据评分设置颜色
            if (qualityScore >= 5) {
                tvQualityScore.setTextColor(Color.parseColor("#4CAF50"));
            } else if (qualityScore >= 3) {
                tvQualityScore.setTextColor(Color.parseColor("#FF9800"));
            } else {
                tvQualityScore.setTextColor(Color.parseColor("#F44336"));
            }
            
            // 设置详细评分
            tvServiceAttitude.setText("服务态度: " + qualityMonitor.getService_attitude() + "/5");
            tvProfessionalSkill.setText("专业技能: " + qualityMonitor.getProfessional_skill() + "/5");
            tvCommunication.setText("沟通能力: " + qualityMonitor.getCommunication() + "/5");
            
            // 设置评价意见
            if (qualityMonitor.getComments() != null && !qualityMonitor.getComments().trim().isEmpty()) {
                tvComments.setText(qualityMonitor.getComments());
                tvComments.setVisibility(View.VISIBLE);
            } else {
                tvComments.setVisibility(View.GONE);
            }
            
            // 异步加载用户名称
            loadUserNames(qualityMonitor);
        }
        
        private void loadUserNames(QualityMonitor qualityMonitor) {
            executor.execute(new Runnable() {
                @Override
                public void run() {
                    // 获取陪护人员姓名
                    User attendant = database.userDao().getUserByIdSync(qualityMonitor.getAttendant_id());
                    // 获取患者姓名
                    User patient = database.userDao().getUserByIdSync(qualityMonitor.getPatient_id());
                    // 获取医护人员姓名
                    User nurse = database.userDao().getUserByIdSync(qualityMonitor.getNurse_id());
                    
                    // 更新UI
                    if (itemView.getContext() instanceof android.app.Activity) {
                        ((android.app.Activity) itemView.getContext()).runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                if (attendant != null) {
                                    tvAttendantName.setText("陪护: " + attendant.getName());
                                }
                                if (patient != null) {
                                    tvPatientName.setText("患者: " + patient.getName());
                                }
                                if (nurse != null) {
                                    tvNurseName.setText("评价医护: " + nurse.getName());
                                }
                            }
                        });
                    }
                }
            });
        }
    }
} 