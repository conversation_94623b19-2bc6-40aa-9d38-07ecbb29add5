package cn.edu.boyapeihu.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import java.util.List;

import cn.edu.boyapeihu.entity.Payment;

@Dao
public interface PaymentDao {
    
    @Insert
    long insert(Payment payment);
    
    @Update
    void update(Payment payment);
    
    @Delete
    void delete(Payment payment);
    
    @Query("SELECT * FROM payment WHERE id = :id")
    LiveData<Payment> getPaymentById(int id);
    
    @Query("SELECT * FROM payment WHERE patient_id = :patientId ORDER BY payment_time DESC")
    LiveData<List<Payment>> getPaymentsByPatient(int patientId);
    
    @Query("SELECT * FROM payment WHERE attendant_id = :attendantId ORDER BY payment_time DESC")
    LiveData<List<Payment>> getPaymentsByAttendant(int attendantId);
    
    @Query("SELECT * FROM payment ORDER BY payment_time DESC")
    LiveData<List<Payment>> getAllPayments();
    
    @Query("SELECT * FROM payment WHERE payment_time >= :startDate AND payment_time <= :endDate ORDER BY payment_time DESC")
    LiveData<List<Payment>> getPaymentsByDateRange(String startDate, String endDate);
    
    @Query("SELECT SUM(amount) FROM payment WHERE attendant_id = :attendantId")
    LiveData<Double> getTotalAmountByAttendant(int attendantId);
    
    @Query("SELECT SUM(amount) FROM payment WHERE patient_id = :patientId")
    LiveData<Double> getTotalAmountByPatient(int patientId);
    
    @Query("DELETE FROM payment WHERE id = :id")
    void deleteById(int id);
}