package cn.edu.boyapeihu.activity;

import android.content.Intent;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.floatingactionbutton.FloatingActionButton;

import java.util.List;

import cn.edu.boyapeihu.R;
import cn.edu.boyapeihu.adapter.PaymentAdapter;
import cn.edu.boyapeihu.database.AppDatabase;
import cn.edu.boyapeihu.entity.Payment;
import cn.edu.boyapeihu.utils.Constants;
import cn.edu.boyapeihu.utils.SessionManager;

public class PaymentActivity extends AppCompatActivity {
    
    private RecyclerView recyclerView;
    private FloatingActionButton fabAdd;
    private PaymentAdapter adapter;
    
    private AppDatabase database;
    private SessionManager sessionManager;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_payment);
        
        initViews();
        initData();
        loadPayments();
    }
    
    private void initViews() {
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle("缴费管理");
        
        recyclerView = findViewById(R.id.recycler_view);
        fabAdd = findViewById(R.id.fab_add);
        
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        adapter = new PaymentAdapter(this);
        recyclerView.setAdapter(adapter);
        
        fabAdd.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                addPayment();
            }
        });
    }
    
    private void initData() {
        database = AppDatabase.getDatabase(this);
        sessionManager = new SessionManager(this);
        
        // 根据角色显示不同内容
        String userRole = sessionManager.getCurrentUserRole();
        if (Constants.ROLE_PATIENT.equals(userRole)) {
            // 患者可以添加缴费记录
            fabAdd.setVisibility(View.VISIBLE);
        } else {
            // 医护和陪护只能查看
            fabAdd.setVisibility(View.GONE);
        }
    }
    
    private void loadPayments() {
        int currentUserId = sessionManager.getCurrentUserId();
        String userRole = sessionManager.getCurrentUserRole();
        
        if (Constants.ROLE_NURSE.equals(userRole)) {
            // 医护人员查看所有缴费记录
            database.paymentDao().getAllPayments().observe(this, new Observer<List<Payment>>() {
                @Override
                public void onChanged(List<Payment> payments) {
                    adapter.setPayments(payments);
                }
            });
        } else if (Constants.ROLE_ATTENDANT.equals(userRole)) {
            // 陪护人员查看与自己相关的缴费记录
            database.paymentDao().getPaymentsByAttendant(currentUserId).observe(this, new Observer<List<Payment>>() {
                @Override
                public void onChanged(List<Payment> payments) {
                    adapter.setPayments(payments);
                }
            });
        } else if (Constants.ROLE_PATIENT.equals(userRole)) {
            // 患者查看自己的缴费记录
            database.paymentDao().getPaymentsByPatient(currentUserId).observe(this, new Observer<List<Payment>>() {
                @Override
                public void onChanged(List<Payment> payments) {
                    adapter.setPayments(payments);
                }
            });
        }
    }
    
    private void addPayment() {
        Intent intent = new Intent(this, AddPaymentActivity.class);
        startActivity(intent);
    }
    
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.payment_menu, menu);
        return true;
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        
        if (id == android.R.id.home) {
            finish();
            return true;
        } else if (id == R.id.action_filter) {
            // TODO: 实现筛选功能
            Toast.makeText(this, "筛选功能开发中", Toast.LENGTH_SHORT).show();
            return true;
        } else if (id == R.id.action_statistics) {
            // TODO: 实现统计功能
            Toast.makeText(this, "统计功能开发中", Toast.LENGTH_SHORT).show();
            return true;
        }
        
        return super.onOptionsItemSelected(item);
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        // 返回时刷新列表
        loadPayments();
    }
} 