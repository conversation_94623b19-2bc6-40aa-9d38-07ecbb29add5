package cn.edu.boyapeihu.activity;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.RatingBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.lifecycle.Observer;

import cn.edu.boyapeihu.R;
import cn.edu.boyapeihu.database.AppDatabase;
import cn.edu.boyapeihu.entity.Caregiver;

public class CaregiverDetailActivity extends AppCompatActivity {
    
    private ImageView ivBack, ivAvatar, ivCall, ivMessage;
    private TextView tvTitle, tvName, tvTitle2, tvLevel, tvQuote, tvDescription;
    private TextView tvExperience, tvSpecialties, tvEducation, tvCertifications;
    private TextView tvWorkYears, tvPhone, tvEmail, tvStatus;
    private RatingBar ratingBar;
    private TextView tvRating, tvReviewCount;
    
    private AppDatabase database;
    private int caregiverId;
    private Caregiver currentCaregiver;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_caregiver_detail);
        
        initViews();
        initData();
        setupClickListeners();
    }
    
    private void initViews() {
        ivBack = findViewById(R.id.iv_back);
        tvTitle = findViewById(R.id.tv_title);
        ivAvatar = findViewById(R.id.iv_avatar);
        tvName = findViewById(R.id.tv_name);
        tvTitle2 = findViewById(R.id.tv_title2);
        tvLevel = findViewById(R.id.tv_level);
        tvQuote = findViewById(R.id.tv_quote);
        tvDescription = findViewById(R.id.tv_description);
        tvExperience = findViewById(R.id.tv_experience);
        tvSpecialties = findViewById(R.id.tv_specialties);
        tvEducation = findViewById(R.id.tv_education);
        tvCertifications = findViewById(R.id.tv_certifications);
        tvWorkYears = findViewById(R.id.tv_work_years);
        tvPhone = findViewById(R.id.tv_phone);
        tvEmail = findViewById(R.id.tv_email);
        tvStatus = findViewById(R.id.tv_status);
        ratingBar = findViewById(R.id.rating_bar);
        tvRating = findViewById(R.id.tv_rating);
        tvReviewCount = findViewById(R.id.tv_review_count);
        ivCall = findViewById(R.id.iv_call);
        ivMessage = findViewById(R.id.iv_message);
        
        tvTitle.setText("护理员详情");
    }
    
    private void initData() {
        database = AppDatabase.getDatabase(this);
        
        // 获取传递的护理员ID
        caregiverId = getIntent().getIntExtra("caregiver_id", -1);
        if (caregiverId == -1) {
            Toast.makeText(this, "护理员信息错误", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }
        
        loadCaregiverDetail();
    }
    
    private void loadCaregiverDetail() {
        database.caregiverDao().getCaregiverById(caregiverId).observe(this, new Observer<Caregiver>() {
            @Override
            public void onChanged(Caregiver caregiver) {
                if (caregiver != null) {
                    currentCaregiver = caregiver;
                    displayCaregiverInfo(caregiver);
                }
            }
        });
    }
    
    private void displayCaregiverInfo(Caregiver caregiver) {
        tvName.setText(caregiver.getName());
        tvTitle2.setText(caregiver.getTitle());
        tvLevel.setText(caregiver.getLevel());
        tvQuote.setText("\"" + caregiver.getQuote() + "\"");
        tvDescription.setText(caregiver.getDescription());
        tvExperience.setText(caregiver.getExperience());
        tvSpecialties.setText(caregiver.getSpecialties());
        tvEducation.setText(caregiver.getEducation());
        tvCertifications.setText(caregiver.getCertifications());
        tvWorkYears.setText(caregiver.getWorkYears() + "年");
        tvPhone.setText(caregiver.getPhone());
        tvEmail.setText(caregiver.getEmail());
        
        // 设置状态显示
        String status = caregiver.getStatus();
        if ("available".equals(status)) {
            tvStatus.setText("空闲");
            tvStatus.setTextColor(getResources().getColor(R.color.green));
        } else if ("busy".equals(status)) {
            tvStatus.setText("忙碌");
            tvStatus.setTextColor(getResources().getColor(R.color.orange));
        } else {
            tvStatus.setText("离线");
            tvStatus.setTextColor(getResources().getColor(R.color.gray));
        }
        
        // 设置评分
        ratingBar.setRating((float) caregiver.getRating());
        tvRating.setText(String.format("%.1f", caregiver.getRating()));
        tvReviewCount.setText("(" + caregiver.getReviewCount() + "条评价)");
        
        // 设置等级背景色
        if ("高级".equals(caregiver.getLevel())) {
            tvLevel.setBackgroundResource(R.drawable.bg_blue_badge);
        } else if ("中级".equals(caregiver.getLevel())) {
            tvLevel.setBackgroundResource(R.drawable.bg_yellow_badge);
        } else {
            tvLevel.setBackgroundResource(R.drawable.bg_gray_badge);
        }
    }
    
    private void setupClickListeners() {
        ivBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        
        ivCall.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (currentCaregiver != null && currentCaregiver.getPhone() != null) {
                    Intent intent = new Intent(Intent.ACTION_DIAL);
                    intent.setData(Uri.parse("tel:" + currentCaregiver.getPhone()));
                    startActivity(intent);
                }
            }
        });
        
        ivMessage.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Toast.makeText(CaregiverDetailActivity.this, "发送消息功能", Toast.LENGTH_SHORT).show();
            }
        });
    }
}
