package cn.edu.boyapeihu.activity;

import android.app.DatePickerDialog;
import android.app.TimePickerDialog;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.MenuItem;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TimePicker;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.lifecycle.Observer;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import cn.edu.boyapeihu.R;
import cn.edu.boyapeihu.database.AppDatabase;
import cn.edu.boyapeihu.entity.Payment;
import cn.edu.boyapeihu.entity.User;
import cn.edu.boyapeihu.utils.Constants;
import cn.edu.boyapeihu.utils.DateTimeUtils;
import cn.edu.boyapeihu.utils.SessionManager;

public class AddPaymentActivity extends AppCompatActivity {
    
    private EditText etPaymentDate, etPaymentTime, etAmount, etDescription;
    private Spinner spinnerAttendant, spinnerPaymentType;
    private Button btnSave;
    
    private AppDatabase database;
    private SessionManager sessionManager;
    private ExecutorService executor;
    
    private List<User> attendantList;
    private ArrayAdapter<String> attendantAdapter;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_add_payment);
        
        initViews();
        initData();
        loadAttendants();
        setListeners();
    }
    
    private void initViews() {
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle("添加缴费记录");
        
        etPaymentDate = findViewById(R.id.et_payment_date);
        etPaymentTime = findViewById(R.id.et_payment_time);
        etAmount = findViewById(R.id.et_amount);
        etDescription = findViewById(R.id.et_description);
        spinnerAttendant = findViewById(R.id.spinner_attendant);
        spinnerPaymentType = findViewById(R.id.spinner_payment_type);
        btnSave = findViewById(R.id.btn_save);
        
        // 设置默认时间
        setDefaultDateTime();
        
        // 设置缴费类型
        setupPaymentTypes();
    }
    
    private void initData() {
        database = AppDatabase.getDatabase(this);
        sessionManager = new SessionManager(this);
        executor = Executors.newSingleThreadExecutor();
        
        attendantList = new ArrayList<>();

        attendantAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_dropdown_item, new ArrayList<String>());
        spinnerAttendant.setAdapter(attendantAdapter);
    }
    
    private void setDefaultDateTime() {
        String currentDate = DateTimeUtils.getCurrentDate();
        String currentTime = DateTimeUtils.getCurrentTime();
        etPaymentDate.setText(currentDate);
        etPaymentTime.setText(currentTime);
    }
    
    private void setupPaymentTypes() {
        String[] paymentTypes = {
            "护理费",
            "陪护费",
            "医疗费",
            "康复费",
            "其他费用"
        };
        
        ArrayAdapter<String> paymentTypeAdapter = new ArrayAdapter<>(this, 
                android.R.layout.simple_spinner_dropdown_item, paymentTypes);
        spinnerPaymentType.setAdapter(paymentTypeAdapter);
    }
    
    private void loadAttendants() {
        // 加载陪护人员列表
        database.userDao().getUsersByRole(Constants.ROLE_ATTENDANT).observe(this, new Observer<List<User>>() {
            @Override
            public void onChanged(List<User> users) {
                attendantList.clear();
                attendantAdapter.clear();
                
                // 添加"无指定陪护"选项
                attendantList.add(null);
                attendantAdapter.add("无指定陪护");
                
                for (User user : users) {
                    attendantList.add(user);
                    attendantAdapter.add(user.getName());
                }
                attendantAdapter.notifyDataSetChanged();
            }
        });
    }
    
    private void setListeners() {
        // 缴费日期选择
        etPaymentDate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showDatePicker();
            }
        });
        
        // 缴费时间选择
        etPaymentTime.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showTimePicker();
            }
        });
        
        // 保存按钮
        btnSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                savePayment();
            }
        });
    }
    
    private void showDatePicker() {
        Calendar calendar = Calendar.getInstance();
        DatePickerDialog dialog = new DatePickerDialog(this,
                new DatePickerDialog.OnDateSetListener() {
                    @Override
                    public void onDateSet(DatePicker view, int year, int month, int dayOfMonth) {
                        String date = String.format("%04d-%02d-%02d", year, month + 1, dayOfMonth);
                        etPaymentDate.setText(date);
                    }
                },
                calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH));
        dialog.show();
    }
    
    private void showTimePicker() {
        Calendar calendar = Calendar.getInstance();
        TimePickerDialog dialog = new TimePickerDialog(this,
                new TimePickerDialog.OnTimeSetListener() {
                    @Override
                    public void onTimeSet(TimePicker view, int hourOfDay, int minute) {
                        String time = String.format("%02d:%02d", hourOfDay, minute);
                        etPaymentTime.setText(time);
                    }
                },
                calendar.get(Calendar.HOUR_OF_DAY),
                calendar.get(Calendar.MINUTE),
                true);
        dialog.show();
    }
    
    private void savePayment() {
        String paymentDate = etPaymentDate.getText().toString().trim();
        String paymentTime = etPaymentTime.getText().toString().trim();
        String amountStr = etAmount.getText().toString().trim();
        String description = etDescription.getText().toString().trim();
        
        if (TextUtils.isEmpty(paymentDate) || TextUtils.isEmpty(paymentTime)) {
            Toast.makeText(this, "请选择缴费时间", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (TextUtils.isEmpty(amountStr)) {
            Toast.makeText(this, "请输入缴费金额", Toast.LENGTH_SHORT).show();
            return;
        }
        
        double amount;
        try {
            amount = Double.parseDouble(amountStr);
            if (amount <= 0) {
                Toast.makeText(this, "金额必须大于0", Toast.LENGTH_SHORT).show();
                return;
            }
        } catch (NumberFormatException e) {
            Toast.makeText(this, "请输入有效的金额", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (spinnerPaymentType.getSelectedItemPosition() < 0) {
            Toast.makeText(this, "请选择缴费类型", Toast.LENGTH_SHORT).show();
            return;
        }
        
        String paymentDateTime = paymentDate + " " + paymentTime + ":00";
        String selectedPaymentType = (String) spinnerPaymentType.getSelectedItem();
        
        // 获取选择的陪护人员（可能为null）
        User selectedAttendant = null;
        if (spinnerAttendant.getSelectedItemPosition() > 0) {
            selectedAttendant = attendantList.get(spinnerAttendant.getSelectedItemPosition());
        }
        
        btnSave.setEnabled(false);
        btnSave.setText("保存中...");
        
        final User finalSelectedAttendant = selectedAttendant;
        
        executor.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    Payment payment = new Payment(
                            sessionManager.getCurrentUserId(),                           // 当前患者ID
                            finalSelectedAttendant != null ? finalSelectedAttendant.getId() : 0,  // 陪护ID（可选）
                            amount,                                                     // 金额
                            paymentDateTime,                                           // 缴费时间
                            selectedPaymentType                                        // 缴费类型
                    );
                    
                    // 设置状态
                    payment.setStatus("已支付");
                    
                    // 设置描述
                    if (!TextUtils.isEmpty(description)) {
                        payment.setDescription(description);
                    }
                    
                    long result = database.paymentDao().insert(payment);
                    
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            btnSave.setEnabled(true);
                            btnSave.setText("保存");
                            
                            if (result > 0) {
                                Toast.makeText(AddPaymentActivity.this, "缴费记录保存成功", Toast.LENGTH_SHORT).show();
                                finish();
                            } else {
                                Toast.makeText(AddPaymentActivity.this, "缴费记录保存失败", Toast.LENGTH_SHORT).show();
                            }
                        }
                    });
                } catch (Exception e) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            btnSave.setEnabled(true);
                            btnSave.setText("保存");
                            Toast.makeText(AddPaymentActivity.this, "保存失败：" + e.getMessage(), Toast.LENGTH_SHORT).show();
                        }
                    });
                }
            }
        });
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }
} 