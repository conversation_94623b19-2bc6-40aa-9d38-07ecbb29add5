<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        android:elevation="4dp"
        android:theme="@style/ThemeOverlay.AppCompat.ActionBar"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 标题 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="新增排班记录"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/primary"
            android:gravity="center"
            android:layout_marginBottom="24dp" />

        <!-- 陪护人员选择 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="陪护人员"
            android:textSize="16sp"
            android:textColor="@color/primary"
            android:layout_marginBottom="8dp" />

        <Spinner
            android:id="@+id/spinner_attendant"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:background="@drawable/bg_status_pill"
            android:padding="12dp" />

        <!-- 患者选择 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="患者"
            android:textSize="16sp"
            android:textColor="@color/primary"
            android:layout_marginBottom="8dp" />

        <Spinner
            android:id="@+id/spinner_patient"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:background="@drawable/bg_status_pill"
            android:padding="12dp" />

        <!-- 开始日期 -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_start_date"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="开始日期"
                android:inputType="none"
                android:focusable="false"
                android:clickable="true"
                android:drawableEnd="@drawable/ic_time"
                android:textSize="16sp" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 开始时间 -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_start_time"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="开始时间"
                android:inputType="none"
                android:focusable="false"
                android:clickable="true"
                android:drawableEnd="@drawable/ic_time"
                android:textSize="16sp" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 结束日期 -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_end_date"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="结束日期"
                android:inputType="none"
                android:focusable="false"
                android:clickable="true"
                android:drawableEnd="@drawable/ic_time"
                android:textSize="16sp" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 结束时间 -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_end_time"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="结束时间"
                android:inputType="none"
                android:focusable="false"
                android:clickable="true"
                android:drawableEnd="@drawable/ic_time"
                android:textSize="16sp" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 备注 -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_notes"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="备注（可选）"
                android:inputType="textMultiLine"
                android:lines="3"
                android:gravity="top"
                android:textSize="16sp" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 操作按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <Button
                android:id="@+id/btn_cancel"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:text="取消"
                android:textSize="16sp"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

            <Button
                android:id="@+id/btn_save"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:text="保存"
                android:textSize="16sp"
                style="@style/Widget.MaterialComponents.Button" />

        </LinearLayout>

    </LinearLayout>

    </ScrollView>

</LinearLayout> 