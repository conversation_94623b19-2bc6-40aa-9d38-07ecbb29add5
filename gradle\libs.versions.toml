[versions]
agp = "8.11.1"
junit = "4.13.2"
junitVersion = "1.2.1"
espressoCore = "3.6.1"
appcompat = "1.7.1"
material = "1.12.0"
room = "2.6.1"
lifecycle = "2.8.7"
recyclerview = "1.3.2"
glide = "4.16.0"
cardview = "1.0.0"

[libraries]
junit = { group = "junit", name = "junit", version.ref = "junit" }
ext-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }

# Room dependencies
room-runtime = { group = "androidx.room", name = "room-runtime", version.ref = "room" }
room-compiler = { group = "androidx.room", name = "room-compiler", version.ref = "room" }

# Lifecycle dependencies
lifecycle-viewmodel = { group = "androidx.lifecycle", name = "lifecycle-viewmodel", version.ref = "lifecycle" }
lifecycle-livedata = { group = "androidx.lifecycle", name = "lifecycle-livedata", version.ref = "lifecycle" }

# UI dependencies
recyclerview = { group = "androidx.recyclerview", name = "recyclerview", version.ref = "recyclerview" }
cardview = { group = "androidx.cardview", name = "cardview", version.ref = "cardview" }

# Image loading
glide = { group = "com.github.bumptech.glide", name = "glide", version.ref = "glide" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }

