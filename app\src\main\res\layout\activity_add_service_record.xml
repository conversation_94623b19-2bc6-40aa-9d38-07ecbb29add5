<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/backgroundColor">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        android:theme="@style/ThemeOverlay.AppCompat.ActionBar"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 患者选择 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/cardBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="选择患者"
                        android:textSize="16sp"
                        android:textColor="@color/textColorPrimary"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_patient"
                        android:drawablePadding="8dp" />

                    <Spinner
                        android:id="@+id/spinner_patient"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 服务时间 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/cardBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="服务时间"
                        android:textSize="16sp"
                        android:textColor="@color/textColorPrimary"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_time"
                        android:drawablePadding="8dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <EditText
                            android:id="@+id/et_service_date"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            android:hint="选择日期"
                            android:focusable="false"
                            android:clickable="true"
                            android:background="@drawable/bg_status_pill"
                            android:padding="12dp"
                            android:textSize="14sp" />

                        <EditText
                            android:id="@+id/et_service_time"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            android:hint="选择时间"
                            android:focusable="false"
                            android:clickable="true"
                            android:background="@drawable/bg_status_pill"
                            android:padding="12dp"
                            android:textSize="14sp" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 服务类型 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/cardBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="服务类型"
                        android:textSize="16sp"
                        android:textColor="@color/textColorPrimary"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_service"
                        android:drawablePadding="8dp" />

                    <Spinner
                        android:id="@+id/spinner_service_type"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 服务内容 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/cardBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="服务内容"
                        android:textSize="16sp"
                        android:textColor="@color/textColorPrimary"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_message"
                        android:drawablePadding="8dp" />

                    <EditText
                        android:id="@+id/et_service_content"
                        android:layout_width="match_parent"
                        android:layout_height="120dp"
                        android:hint="请详细描述本次服务内容..."
                        android:gravity="top|start"
                        android:background="@drawable/bg_status_pill"
                        android:padding="12dp"
                        android:textSize="14sp"
                        android:maxLines="6" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 保存按钮 -->
            <Button
                android:id="@+id/btn_save"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="保存服务记录"
                android:textSize="16sp"
                android:textStyle="bold"
                android:background="@drawable/bg_status_pill"
                android:backgroundTint="?attr/colorPrimary"
                android:textColor="@android:color/white"
                android:padding="16dp"
                android:layout_marginTop="16dp" />

        </LinearLayout>

    </ScrollView>

</LinearLayout> 