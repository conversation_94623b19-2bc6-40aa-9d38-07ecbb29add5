package cn.edu.boyapeihu.entity;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.ForeignKey;
import androidx.room.Ignore;

@Entity(tableName = "quality_monitor",
        foreignKeys = {
            @ForeignKey(entity = User.class, parentColumns = "id", childColumns = "nurse_id"),
            @ForeignKey(entity = User.class, parentColumns = "id", childColumns = "attendant_id"),
            @ForeignKey(entity = User.class, parentColumns = "id", childColumns = "patient_id")
        })
public class QualityMonitor {
    @PrimaryKey(autoGenerate = true)
    private int id;
    
    private int nurse_id;
    private int attendant_id;
    private Integer patient_id; // 可为空
    private String evaluation_time;    // 评估时间
    private int quality_score;         // 总质量评分
    private int service_attitude;      // 服务态度评分
    private int professional_skill;    // 专业技能评分
    private int communication;         // 沟通能力评分
    private String comments;           // 评价备注
    
    // 构造函数
    public QualityMonitor() {}
    
    @Ignore
    public QualityMonitor(int nurse_id, int attendant_id, String evaluation_time, int quality_score) {
        this.nurse_id = nurse_id;
        this.attendant_id = attendant_id;
        this.evaluation_time = evaluation_time;
        this.quality_score = quality_score;
    }
    
    @Ignore
    public QualityMonitor(int nurse_id, int attendant_id, int patient_id, int quality_score, 
                         int service_attitude, int professional_skill, int communication, String comments) {
        this.nurse_id = nurse_id;
        this.attendant_id = attendant_id;
        this.patient_id = patient_id;
        this.quality_score = quality_score;
        this.service_attitude = service_attitude;
        this.professional_skill = professional_skill;
        this.communication = communication;
        this.comments = comments;
    }
    
    // Getter和Setter方法
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public int getNurse_id() {
        return nurse_id;
    }
    
    public void setNurse_id(int nurse_id) {
        this.nurse_id = nurse_id;
    }
    
    public int getAttendant_id() {
        return attendant_id;
    }
    
    public void setAttendant_id(int attendant_id) {
        this.attendant_id = attendant_id;
    }
    
    public Integer getPatient_id() {
        return patient_id;
    }
    
    public void setPatient_id(Integer patient_id) {
        this.patient_id = patient_id;
    }
    
    public String getEvaluation_time() {
        return evaluation_time;
    }
    
    public void setEvaluation_time(String evaluation_time) {
        this.evaluation_time = evaluation_time;
    }
    
    public int getQuality_score() {
        return quality_score;
    }
    
    public void setQuality_score(int quality_score) {
        this.quality_score = quality_score;
    }
    
    public int getService_attitude() {
        return service_attitude;
    }
    
    public void setService_attitude(int service_attitude) {
        this.service_attitude = service_attitude;
    }
    
    public int getProfessional_skill() {
        return professional_skill;
    }
    
    public void setProfessional_skill(int professional_skill) {
        this.professional_skill = professional_skill;
    }
    
    public int getCommunication() {
        return communication;
    }
    
    public void setCommunication(int communication) {
        this.communication = communication;
    }
    
    public String getComments() {
        return comments;
    }
    
    public void setComments(String comments) {
        this.comments = comments;
    }
    
    // 向后兼容的getter方法
    public String getTime() {
        return evaluation_time;
    }
    
    public void setTime(String time) {
        this.evaluation_time = time;
    }
    
    public int getScore() {
        return quality_score;
    }
    
    public void setScore(int score) {
        this.quality_score = score;
    }
    
    public String getComment() {
        return comments;
    }
    
    public void setComment(String comment) {
        this.comments = comment;
    }
} 