package cn.edu.boyapeihu.entity;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.ForeignKey;
import androidx.room.Ignore;

@Entity(tableName = "service_record",
        foreignKeys = {
            @ForeignKey(entity = User.class, parentColumns = "id", childColumns = "attendant_id"),
            @ForeignKey(entity = User.class, parentColumns = "id", childColumns = "patient_id")
        })
public class ServiceRecord {
    @PrimaryKey(autoGenerate = true)
    private int id;
    
    private int attendant_id;
    private int patient_id;
    private String service_time;     // 服务时间
    private String service_type;     // 服务类型
    private String service_content;  // 服务内容
    private Integer rating;          // 满意度评价（1-5分）
    
    // 构造函数
    public ServiceRecord() {}
    
    @Ignore
    public ServiceRecord(int attendant_id, int patient_id, String service_time, String service_content) {
        this.attendant_id = attendant_id;
        this.patient_id = patient_id;
        this.service_time = service_time;
        this.service_content = service_content;
    }
    
    @Ignore
    public ServiceRecord(int attendant_id, int patient_id, String service_type, String service_time, String service_content) {
        this.attendant_id = attendant_id;
        this.patient_id = patient_id;
        this.service_type = service_type;
        this.service_time = service_time;
        this.service_content = service_content;
    }
    
    // Getter和Setter方法
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public int getAttendant_id() {
        return attendant_id;
    }
    
    public void setAttendant_id(int attendant_id) {
        this.attendant_id = attendant_id;
    }
    
    public int getPatient_id() {
        return patient_id;
    }
    
    public void setPatient_id(int patient_id) {
        this.patient_id = patient_id;
    }
    
    public String getService_time() {
        return service_time;
    }
    
    public void setService_time(String service_time) {
        this.service_time = service_time;
    }
    
    public String getService_type() {
        return service_type;
    }
    
    public void setService_type(String service_type) {
        this.service_type = service_type;
    }
    
    public String getService_content() {
        return service_content;
    }
    
    public void setService_content(String service_content) {
        this.service_content = service_content;
    }
    
    public Integer getRating() {
        return rating;
    }
    
    public void setRating(Integer rating) {
        this.rating = rating;
    }
    
    // 向后兼容的getter方法
    public String getTime() {
        return service_time;
    }
    
    public void setTime(String time) {
        this.service_time = time;
    }
    
    public String getContent() {
        return service_content;
    }
    
    public void setContent(String content) {
        this.service_content = content;
    }
} 