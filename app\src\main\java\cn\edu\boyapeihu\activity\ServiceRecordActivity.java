package cn.edu.boyapeihu.activity;

import android.content.Intent;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.floatingactionbutton.FloatingActionButton;

import java.util.List;

import cn.edu.boyapeihu.R;
import cn.edu.boyapeihu.adapter.ServiceRecordAdapter;
import cn.edu.boyapeihu.database.AppDatabase;
import cn.edu.boyapeihu.entity.ServiceRecord;
import cn.edu.boyapeihu.utils.Constants;
import cn.edu.boyapeihu.utils.SessionManager;

public class ServiceRecordActivity extends AppCompatActivity {
    
    private RecyclerView recyclerView;
    private FloatingActionButton fabAdd;
    private ServiceRecordAdapter adapter;
    
    private AppDatabase database;
    private SessionManager sessionManager;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_service_record);
        
        initViews();
        initData();
        loadServiceRecords();
    }
    
    private void initViews() {
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle("服务记录");
        
        recyclerView = findViewById(R.id.recycler_view);
        fabAdd = findViewById(R.id.fab_add);
        
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        adapter = new ServiceRecordAdapter(this);
        recyclerView.setAdapter(adapter);
        
        fabAdd.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                addServiceRecord();
            }
        });
    }
    
    private void initData() {
        database = AppDatabase.getDatabase(this);
        sessionManager = new SessionManager(this);
        
        // 根据角色显示不同内容
        String userRole = sessionManager.getCurrentUserRole();
        if (Constants.ROLE_ATTENDANT.equals(userRole)) {
            // 陪护人员可以添加服务记录
            fabAdd.setVisibility(View.VISIBLE);
        } else {
            // 医护和患者只能查看
            fabAdd.setVisibility(View.GONE);
        }
    }
    
    private void loadServiceRecords() {
        int currentUserId = sessionManager.getCurrentUserId();
        String userRole = sessionManager.getCurrentUserRole();
        
        if (Constants.ROLE_NURSE.equals(userRole)) {
            // 医护人员查看所有服务记录
            database.serviceRecordDao().getAllServiceRecords().observe(this, new Observer<List<ServiceRecord>>() {
                @Override
                public void onChanged(List<ServiceRecord> serviceRecords) {
                    adapter.setServiceRecords(serviceRecords);
                }
            });
        } else if (Constants.ROLE_ATTENDANT.equals(userRole)) {
            // 陪护人员查看自己的服务记录
            database.serviceRecordDao().getServiceRecordsByAttendant(currentUserId).observe(this, new Observer<List<ServiceRecord>>() {
                @Override
                public void onChanged(List<ServiceRecord> serviceRecords) {
                    adapter.setServiceRecords(serviceRecords);
                }
            });
        } else if (Constants.ROLE_PATIENT.equals(userRole)) {
            // 患者查看与自己相关的服务记录
            database.serviceRecordDao().getServiceRecordsByPatient(currentUserId).observe(this, new Observer<List<ServiceRecord>>() {
                @Override
                public void onChanged(List<ServiceRecord> serviceRecords) {
                    adapter.setServiceRecords(serviceRecords);
                }
            });
        }
    }
    
    private void addServiceRecord() {
        Intent intent = new Intent(this, AddServiceRecordActivity.class);
        startActivity(intent);
    }
    
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.service_record_menu, menu);
        return true;
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        
        if (id == android.R.id.home) {
            finish();
            return true;
        } else if (id == R.id.action_filter) {
            // TODO: 实现筛选功能
            Toast.makeText(this, "筛选功能开发中", Toast.LENGTH_SHORT).show();
            return true;
        }
        
        return super.onOptionsItemSelected(item);
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        // 返回时刷新列表
        loadServiceRecords();
    }
} 