package cn.edu.boyapeihu.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import java.util.List;

import cn.edu.boyapeihu.entity.ServiceRecord;

@Dao
public interface ServiceRecordDao {
    
    @Insert
    long insert(ServiceRecord serviceRecord);
    
    @Update
    void update(ServiceRecord serviceRecord);
    
    @Delete
    void delete(ServiceRecord serviceRecord);
    
    @Query("SELECT * FROM service_record WHERE id = :id")
    LiveData<ServiceRecord> getServiceRecordById(int id);
    
    @Query("SELECT * FROM service_record WHERE attendant_id = :attendantId ORDER BY service_time DESC")
    LiveData<List<ServiceRecord>> getServiceRecordsByAttendant(int attendantId);
    
    @Query("SELECT * FROM service_record WHERE patient_id = :patientId ORDER BY service_time DESC")
    LiveData<List<ServiceRecord>> getServiceRecordsByPatient(int patientId);
    
    @Query("SELECT * FROM service_record ORDER BY service_time DESC")
    LiveData<List<ServiceRecord>> getAllServiceRecords();
    
    @Query("SELECT * FROM service_record WHERE service_time >= :startDate AND service_time <= :endDate ORDER BY service_time DESC")
    LiveData<List<ServiceRecord>> getServiceRecordsByDateRange(String startDate, String endDate);
    
    @Query("SELECT * FROM service_record WHERE attendant_id = :attendantId AND service_time >= :startDate AND service_time <= :endDate ORDER BY service_time DESC")
    LiveData<List<ServiceRecord>> getServiceRecordsByAttendantAndDateRange(int attendantId, String startDate, String endDate);
    
    @Query("SELECT AVG(rating) FROM service_record WHERE attendant_id = :attendantId AND rating IS NOT NULL")
    LiveData<Double> getAverageRatingByAttendant(int attendantId);
    
    @Query("UPDATE service_record SET rating = :rating WHERE id = :id")
    void updateRating(int id, int rating);
    
    @Query("DELETE FROM service_record WHERE id = :id")
    void deleteById(int id);
} 