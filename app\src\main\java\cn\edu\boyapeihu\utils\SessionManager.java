package cn.edu.boyapeihu.utils;

import android.content.Context;
import android.content.SharedPreferences;

import cn.edu.boyapeihu.entity.User;

public class SessionManager {
    
    private SharedPreferences prefs;
    private SharedPreferences.Editor editor;
    private Context context;
    
    public SessionManager(Context context) {
        this.context = context;
        prefs = context.getSharedPreferences(Constants.PREF_NAME, Context.MODE_PRIVATE);
        editor = prefs.edit();
    }
    
    /**
     * 保存用户登录信息
     */
    public void saveUserSession(User user) {
        editor.putInt(Constants.PREF_USER_ID, user.getId());
        editor.putString(Constants.PREF_USER_NAME, user.getName());
        editor.putString(Constants.PREF_USER_ROLE, user.getRole());
        editor.putString(Constants.PREF_USER_PHONE, user.getPhone());
        editor.putBoolean(Constants.PREF_IS_LOGGED_IN, true);
        editor.apply();
    }
    
    /**
     * 获取当前登录用户ID
     */
    public int getCurrentUserId() {
        return prefs.getInt(Constants.PREF_USER_ID, -1);
    }
    
    /**
     * 获取当前登录用户姓名
     */
    public String getCurrentUserName() {
        return prefs.getString(Constants.PREF_USER_NAME, "");
    }
    
    /**
     * 获取当前登录用户角色
     */
    public String getCurrentUserRole() {
        return prefs.getString(Constants.PREF_USER_ROLE, "");
    }
    
    /**
     * 获取当前登录用户手机号
     */
    public String getCurrentUserPhone() {
        return prefs.getString(Constants.PREF_USER_PHONE, "");
    }
    
    /**
     * 检查用户是否已登录
     */
    public boolean isLoggedIn() {
        return prefs.getBoolean(Constants.PREF_IS_LOGGED_IN, false);
    }
    
    /**
     * 用户登出，清除所有会话信息
     */
    public void logout() {
        editor.clear();
        editor.apply();
    }
    
    /**
     * 更新用户姓名
     */
    public void updateUserName(String name) {
        editor.putString(Constants.PREF_USER_NAME, name);
        editor.apply();
    }
    
    /**
     * 更新用户手机号
     */
    public void updateUserPhone(String phone) {
        editor.putString(Constants.PREF_USER_PHONE, phone);
        editor.apply();
    }
    
    /**
     * 检查当前用户是否为医护人员
     */
    public boolean isNurse() {
        return Constants.ROLE_NURSE.equals(getCurrentUserRole());
    }
    
    /**
     * 检查当前用户是否为陪护人员
     */
    public boolean isAttendant() {
        return Constants.ROLE_ATTENDANT.equals(getCurrentUserRole());
    }
    
    /**
     * 检查当前用户是否为患者
     */
    public boolean isPatient() {
        return Constants.ROLE_PATIENT.equals(getCurrentUserRole());
    }
    
    /**
     * 获取角色显示名称
     */
    public static String getRoleDisplayName(String role) {
        switch (role) {
            case Constants.ROLE_NURSE:
                return "医护人员";
            case Constants.ROLE_ATTENDANT:
                return "陪护人员";
            case Constants.ROLE_PATIENT:
                return "患者";
            default:
                return "未知角色";
        }
    }
    
    /**
     * 获取当前用户角色显示名称
     */
    public String getCurrentUserRoleDisplayName() {
        return getRoleDisplayName(getCurrentUserRole());
    }
} 