plugins {
    alias(libs.plugins.android.application)
}

android {
    namespace 'cn.edu.boyapeihu'
    compileSdk 36

    defaultConfig {
        applicationId "cn.edu.boyapeihu"
        minSdk 27
        targetSdk 36
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildFeatures {
        dataBinding true
        viewBinding true
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
}

dependencies {

    implementation libs.appcompat
    implementation libs.material
    
    // Room
    implementation libs.room.runtime
    annotationProcessor libs.room.compiler
    
    // Lifecycle
    implementation libs.lifecycle.viewmodel
    implementation libs.lifecycle.livedata
    
    // UI
    implementation libs.recyclerview
    implementation libs.cardview
    
    // Image loading
    implementation libs.glide
    
    testImplementation libs.junit
    androidTestImplementation libs.ext.junit
    androidTestImplementation libs.espresso.core
}