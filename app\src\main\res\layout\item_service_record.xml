<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:cardBackgroundColor="@color/cardBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 服务时间 -->
        <TextView
            android:id="@+id/tv_service_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="服务时间"
            android:textSize="14sp"
            android:textColor="@color/textColorSecondary"
            android:layout_marginBottom="8dp"
            android:drawableStart="@drawable/ic_time"
            android:drawablePadding="8dp" />

        <!-- 人员信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/tv_attendant_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="陪护: 张三"
                android:textSize="16sp"
                android:textColor="@color/textColorPrimary"
                android:textStyle="bold"
                android:drawableStart="@drawable/ic_person"
                android:drawablePadding="8dp" />

            <TextView
                android:id="@+id/tv_patient_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="患者: 李四"
                android:textSize="16sp"
                android:textColor="@color/textColorPrimary"
                android:textStyle="bold"
                android:drawableStart="@drawable/ic_patient"
                android:drawablePadding="8dp" />

        </LinearLayout>

        <!-- 服务类型 -->
        <TextView
            android:id="@+id/tv_service_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="服务类型"
            android:textSize="16sp"
            android:textColor="@color/textColorPrimary"
            android:textStyle="bold"
            android:layout_marginBottom="8dp"
            android:drawableStart="@drawable/ic_service"
            android:drawablePadding="8dp" />

        <!-- 服务内容 -->
        <TextView
            android:id="@+id/tv_service_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="服务内容详情"
            android:textSize="14sp"
            android:textColor="@color/textColorSecondary"
            android:layout_marginBottom="8dp"
            android:maxLines="3"
            android:ellipsize="end" />

        <!-- 评分 -->
        <TextView
            android:id="@+id/tv_rating"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="评分: 5/5"
            android:textSize="14sp"
            android:textColor="@color/colorPrimary"
            android:textStyle="bold"
            android:background="@drawable/bg_status_pill"
            android:padding="8dp"
            android:visibility="gone" />

    </LinearLayout>

</androidx.cardview.widget.CardView> 