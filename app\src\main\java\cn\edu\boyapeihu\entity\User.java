package cn.edu.boyapeihu.entity;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.Ignore;

@Entity(tableName = "user")
public class User {
    @PrimaryKey(autoGenerate = true)
    private int id;
    
    private String name;
    private String role; // nurse, attendant, patient
    private String phone;
    private String password;
    private String avatar;
    
    // 构造函数
    public User() {}
    
    @Ignore
    public User(String name, String role, String phone, String password) {
        this.name = name;
        this.role = role;
        this.phone = phone;
        this.password = password;
    }
    
    // Getter和Setter方法
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getRole() {
        return role;
    }
    
    public void setRole(String role) {
        this.role = role;
    }
    
    public String getPhone() {
        return phone;
    }
    
    public void setPhone(String phone) {
        this.phone = phone;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getAvatar() {
        return avatar;
    }
    
    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }
    
    // 角色常量
    public static final String ROLE_NURSE = "nurse";
    public static final String ROLE_ATTENDANT = "attendant";
    public static final String ROLE_PATIENT = "patient";
} 