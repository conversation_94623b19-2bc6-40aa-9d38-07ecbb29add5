package cn.edu.boyapeihu.activity;

import android.app.DatePickerDialog;
import android.app.TimePickerDialog;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.MenuItem;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.RatingBar;
import android.widget.Spinner;
import android.widget.TimePicker;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.lifecycle.Observer;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import cn.edu.boyapeihu.R;
import cn.edu.boyapeihu.database.AppDatabase;
import cn.edu.boyapeihu.entity.QualityMonitor;
import cn.edu.boyapeihu.entity.User;
import cn.edu.boyapeihu.utils.Constants;
import cn.edu.boyapeihu.utils.DateTimeUtils;
import cn.edu.boyapeihu.utils.SessionManager;

public class AddQualityMonitorActivity extends AppCompatActivity {
    
    private EditText etEvaluationDate, etEvaluationTime, etComments;
    private Spinner spinnerAttendant, spinnerPatient;
    private RatingBar rbServiceAttitude, rbProfessionalSkill, rbCommunication;
    private Button btnSave;
    
    private AppDatabase database;
    private SessionManager sessionManager;
    private ExecutorService executor;
    
    private List<User> attendantList;
    private List<User> patientList;
    private ArrayAdapter<String> attendantAdapter;
    private ArrayAdapter<String> patientAdapter;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_add_quality_monitor);
        
        initViews();
        initData();
        loadUsers();
        setListeners();
    }
    
    private void initViews() {
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle("添加质量评价");
        
        etEvaluationDate = findViewById(R.id.et_evaluation_date);
        etEvaluationTime = findViewById(R.id.et_evaluation_time);
        etComments = findViewById(R.id.et_comments);
        spinnerAttendant = findViewById(R.id.spinner_attendant);
        spinnerPatient = findViewById(R.id.spinner_patient);
        rbServiceAttitude = findViewById(R.id.rb_service_attitude);
        rbProfessionalSkill = findViewById(R.id.rb_professional_skill);
        rbCommunication = findViewById(R.id.rb_communication);
        btnSave = findViewById(R.id.btn_save);
        
        // 设置默认时间
        setDefaultDateTime();
    }
    
    private void initData() {
        database = AppDatabase.getDatabase(this);
        sessionManager = new SessionManager(this);
        executor = Executors.newSingleThreadExecutor();
        
        attendantList = new ArrayList<>();
        patientList = new ArrayList<>();

        attendantAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_dropdown_item, new ArrayList<String>());
        patientAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_dropdown_item, new ArrayList<String>());

        spinnerAttendant.setAdapter(attendantAdapter);
        spinnerPatient.setAdapter(patientAdapter);
    }
    
    private void setDefaultDateTime() {
        String currentDate = DateTimeUtils.getCurrentDate();
        String currentTime = DateTimeUtils.getCurrentTime();
        etEvaluationDate.setText(currentDate);
        etEvaluationTime.setText(currentTime);
    }
    
    private void loadUsers() {
        // 加载陪护人员
        database.userDao().getUsersByRole(Constants.ROLE_ATTENDANT).observe(this, new Observer<List<User>>() {
            @Override
            public void onChanged(List<User> users) {
                attendantList.clear();
                attendantAdapter.clear();
                
                for (User user : users) {
                    attendantList.add(user);
                    attendantAdapter.add(user.getName());
                }
                attendantAdapter.notifyDataSetChanged();
            }
        });
        
        // 加载患者
        database.userDao().getUsersByRole(Constants.ROLE_PATIENT).observe(this, new Observer<List<User>>() {
            @Override
            public void onChanged(List<User> users) {
                patientList.clear();
                patientAdapter.clear();
                
                for (User user : users) {
                    patientList.add(user);
                    patientAdapter.add(user.getName());
                }
                patientAdapter.notifyDataSetChanged();
            }
        });
    }
    
    private void setListeners() {
        // 评价日期选择
        etEvaluationDate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showDatePicker();
            }
        });
        
        // 评价时间选择
        etEvaluationTime.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showTimePicker();
            }
        });
        
        // 保存按钮
        btnSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                saveQualityMonitor();
            }
        });
    }
    
    private void showDatePicker() {
        Calendar calendar = Calendar.getInstance();
        DatePickerDialog dialog = new DatePickerDialog(this,
                new DatePickerDialog.OnDateSetListener() {
                    @Override
                    public void onDateSet(DatePicker view, int year, int month, int dayOfMonth) {
                        String date = String.format("%04d-%02d-%02d", year, month + 1, dayOfMonth);
                        etEvaluationDate.setText(date);
                    }
                },
                calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH));
        dialog.show();
    }
    
    private void showTimePicker() {
        Calendar calendar = Calendar.getInstance();
        TimePickerDialog dialog = new TimePickerDialog(this,
                new TimePickerDialog.OnTimeSetListener() {
                    @Override
                    public void onTimeSet(TimePicker view, int hourOfDay, int minute) {
                        String time = String.format("%02d:%02d", hourOfDay, minute);
                        etEvaluationTime.setText(time);
                    }
                },
                calendar.get(Calendar.HOUR_OF_DAY),
                calendar.get(Calendar.MINUTE),
                true);
        dialog.show();
    }
    
    private void saveQualityMonitor() {
        String evaluationDate = etEvaluationDate.getText().toString().trim();
        String evaluationTime = etEvaluationTime.getText().toString().trim();
        String comments = etComments.getText().toString().trim();
        
        if (TextUtils.isEmpty(evaluationDate) || TextUtils.isEmpty(evaluationTime)) {
            Toast.makeText(this, "请选择评价时间", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (spinnerAttendant.getSelectedItemPosition() < 0) {
            Toast.makeText(this, "请选择陪护人员", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (spinnerPatient.getSelectedItemPosition() < 0) {
            Toast.makeText(this, "请选择患者", Toast.LENGTH_SHORT).show();
            return;
        }
        
        int serviceAttitude = (int) rbServiceAttitude.getRating();
        int professionalSkill = (int) rbProfessionalSkill.getRating();
        int communication = (int) rbCommunication.getRating();
        
        if (serviceAttitude == 0 || professionalSkill == 0 || communication == 0) {
            Toast.makeText(this, "请完成所有评分项目", Toast.LENGTH_SHORT).show();
            return;
        }
        
        String evaluationDateTime = evaluationDate + " " + evaluationTime + ":00";
        User selectedAttendant = attendantList.get(spinnerAttendant.getSelectedItemPosition());
        User selectedPatient = patientList.get(spinnerPatient.getSelectedItemPosition());
        
        // 计算综合评分（平均值）
        int qualityScore = (serviceAttitude + professionalSkill + communication) / 3;
        
        btnSave.setEnabled(false);
        btnSave.setText("保存中...");
        
        executor.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    QualityMonitor qualityMonitor = new QualityMonitor(
                            sessionManager.getCurrentUserId(),  // 当前医护人员ID (nurse_id)
                            selectedAttendant.getId(),          // 陪护人员ID (attendant_id)
                            selectedPatient.getId(),            // 患者ID (patient_id)
                            qualityScore,                       // 综合评分 (quality_score)
                            serviceAttitude,                    // 服务态度 (service_attitude)
                            professionalSkill,                  // 专业技能 (professional_skill)
                            communication,                      // 沟通能力 (communication)
                            ""                                  // 评价意见 (comments) - 先设为空字符串
                    );
                    
                    // 设置评价时间
                    qualityMonitor.setEvaluation_time(evaluationDateTime);
                    
                    // 设置评价意见
                    if (!TextUtils.isEmpty(comments)) {
                        qualityMonitor.setComments(comments);
                    }
                    
                    long result = database.qualityMonitorDao().insert(qualityMonitor);
                    
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            btnSave.setEnabled(true);
                            btnSave.setText("保存");
                            
                            if (result > 0) {
                                Toast.makeText(AddQualityMonitorActivity.this, "质量评价保存成功", Toast.LENGTH_SHORT).show();
                                finish();
                            } else {
                                Toast.makeText(AddQualityMonitorActivity.this, "质量评价保存失败", Toast.LENGTH_SHORT).show();
                            }
                        }
                    });
                } catch (Exception e) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            btnSave.setEnabled(true);
                            btnSave.setText("保存");
                            Toast.makeText(AddQualityMonitorActivity.this, "保存失败：" + e.getMessage(), Toast.LENGTH_SHORT).show();
                        }
                    });
                }
            }
        });
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }
} 