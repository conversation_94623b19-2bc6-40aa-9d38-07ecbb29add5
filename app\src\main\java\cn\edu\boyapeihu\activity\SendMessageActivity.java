package cn.edu.boyapeihu.activity;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.MenuItem;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.lifecycle.Observer;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import cn.edu.boyapeihu.R;
import cn.edu.boyapeihu.database.AppDatabase;
import cn.edu.boyapeihu.entity.Message;
import cn.edu.boyapeihu.entity.User;
import cn.edu.boyapeihu.utils.Constants;
import cn.edu.boyapeihu.utils.DateTimeUtils;
import cn.edu.boyapeihu.utils.SessionManager;

public class SendMessageActivity extends AppCompatActivity {
    
    private Spinner spinnerReceiver;
    private EditText etMessage;
    private Button btnSend;
    
    private AppDatabase database;
    private SessionManager sessionManager;
    private ExecutorService executor;
    
    private List<User> receiverList;
    private ArrayAdapter<String> receiverAdapter;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_send_message);
        
        initViews();
        initData();
        loadReceivers();
        setListeners();
    }
    
    private void initViews() {
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle("发送消息");
        
        spinnerReceiver = findViewById(R.id.spinner_receiver);
        etMessage = findViewById(R.id.et_message);
        btnSend = findViewById(R.id.btn_send);
    }
    
    private void initData() {
        database = AppDatabase.getDatabase(this);
        sessionManager = new SessionManager(this);
        executor = Executors.newSingleThreadExecutor();
        
        receiverList = new ArrayList<>();

        receiverAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_dropdown_item, new ArrayList<String>());
        spinnerReceiver.setAdapter(receiverAdapter);
    }
    
    private void loadReceivers() {
        int currentUserId = sessionManager.getCurrentUserId();
        String currentUserRole = sessionManager.getCurrentUserRole();
        
        // 根据当前用户角色加载可以发送消息的用户
        if (Constants.ROLE_NURSE.equals(currentUserRole)) {
            // 医护人员可以给所有陪护和患者发消息
            loadUsersByRoles(new String[]{Constants.ROLE_ATTENDANT, Constants.ROLE_PATIENT}, currentUserId);
        } else if (Constants.ROLE_ATTENDANT.equals(currentUserRole)) {
            // 陪护人员可以给所有医护和患者发消息
            loadUsersByRoles(new String[]{Constants.ROLE_NURSE, Constants.ROLE_PATIENT}, currentUserId);
        } else if (Constants.ROLE_PATIENT.equals(currentUserRole)) {
            // 患者可以给所有医护和陪护发消息
            loadUsersByRoles(new String[]{Constants.ROLE_NURSE, Constants.ROLE_ATTENDANT}, currentUserId);
        }
    }
    
    private void loadUsersByRoles(String[] roles, int excludeUserId) {
        for (String role : roles) {
            database.userDao().getUsersByRole(role).observe(this, new Observer<List<User>>() {
                @Override
                public void onChanged(List<User> users) {
                    for (User user : users) {
                        if (user.getId() != excludeUserId) {
                            // 检查是否已存在，避免重复添加
                            boolean exists = false;
                            for (User existingUser : receiverList) {
                                if (existingUser.getId() == user.getId()) {
                                    exists = true;
                                    break;
                                }
                            }
                            
                            if (!exists) {
                                receiverList.add(user);
                                receiverAdapter.add(user.getName() + " (" + getRoleDisplayName(user.getRole()) + ")");
                            }
                        }
                    }
                    receiverAdapter.notifyDataSetChanged();
                }
            });
        }
    }
    
    private String getRoleDisplayName(String role) {
        switch (role) {
            case Constants.ROLE_NURSE:
                return "医护";
            case Constants.ROLE_ATTENDANT:
                return "陪护";
            case Constants.ROLE_PATIENT:
                return "患者";
            default:
                return "未知";
        }
    }
    
    private void setListeners() {
        btnSend.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                sendMessage();
            }
        });
    }
    
    private void sendMessage() {
        String messageContent = etMessage.getText().toString().trim();
        
        if (TextUtils.isEmpty(messageContent)) {
            Toast.makeText(this, "请输入消息内容", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (spinnerReceiver.getSelectedItemPosition() < 0) {
            Toast.makeText(this, "请选择接收者", Toast.LENGTH_SHORT).show();
            return;
        }
        
        User selectedReceiver = receiverList.get(spinnerReceiver.getSelectedItemPosition());
        
        btnSend.setEnabled(false);
        btnSend.setText("发送中...");
        
        executor.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    String currentTime = DateTimeUtils.getCurrentDateTime();
                    
                    Message message = new Message(
                            sessionManager.getCurrentUserId(),  // 发送者ID
                            selectedReceiver.getId(),           // 接收者ID
                            messageContent,                     // 消息内容
                            currentTime                         // 发送时间
                    );
                    
                    long result = database.messageDao().insert(message);
                    
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            btnSend.setEnabled(true);
                            btnSend.setText("发送");
                            
                            if (result > 0) {
                                Toast.makeText(SendMessageActivity.this, "消息发送成功", Toast.LENGTH_SHORT).show();
                                etMessage.setText(""); // 清空输入框
                                finish(); // 返回消息列表
                            } else {
                                Toast.makeText(SendMessageActivity.this, "消息发送失败", Toast.LENGTH_SHORT).show();
                            }
                        }
                    });
                } catch (Exception e) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            btnSend.setEnabled(true);
                            btnSend.setText("发送");
                            Toast.makeText(SendMessageActivity.this, "发送失败：" + e.getMessage(), Toast.LENGTH_SHORT).show();
                        }
                    });
                }
            }
        });
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }
} 