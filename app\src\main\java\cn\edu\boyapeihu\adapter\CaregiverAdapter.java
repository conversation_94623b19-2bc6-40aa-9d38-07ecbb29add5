package cn.edu.boyapeihu.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.RatingBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;

import cn.edu.boyapeihu.R;
import cn.edu.boyapeihu.entity.Caregiver;

public class CaregiverAdapter extends RecyclerView.Adapter<CaregiverAdapter.CaregiverViewHolder> {
    
    private Context context;
    private List<Caregiver> caregivers;
    private OnItemClickListener onItemClickListener;
    
    public interface OnItemClickListener {
        void onItemClick(Caregiver caregiver);
    }
    
    public CaregiverAdapter(Context context) {
        this.context = context;
        this.caregivers = new ArrayList<>();
    }
    
    @NonNull
    @Override
    public CaregiverViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_caregiver, parent, false);
        return new CaregiverViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull CaregiverViewHolder holder, int position) {
        Caregiver caregiver = caregivers.get(position);
        holder.bind(caregiver);
    }
    
    @Override
    public int getItemCount() {
        return caregivers.size();
    }
    
    public void setCaregivers(List<Caregiver> caregivers) {
        this.caregivers = caregivers;
        notifyDataSetChanged();
    }
    
    public void setOnItemClickListener(OnItemClickListener listener) {
        this.onItemClickListener = listener;
    }
    
    class CaregiverViewHolder extends RecyclerView.ViewHolder {
        
        private ImageView ivAvatar;
        private TextView tvName, tvTitle, tvLevel, tvQuote, tvStatus;
        private RatingBar ratingBar;
        private TextView tvRating, tvReviewCount;
        
        public CaregiverViewHolder(@NonNull View itemView) {
            super(itemView);
            
            ivAvatar = itemView.findViewById(R.id.iv_avatar);
            tvName = itemView.findViewById(R.id.tv_name);
            tvTitle = itemView.findViewById(R.id.tv_title);
            tvLevel = itemView.findViewById(R.id.tv_level);
            tvQuote = itemView.findViewById(R.id.tv_quote);
            tvStatus = itemView.findViewById(R.id.tv_status);
            ratingBar = itemView.findViewById(R.id.rating_bar);
            tvRating = itemView.findViewById(R.id.tv_rating);
            tvReviewCount = itemView.findViewById(R.id.tv_review_count);
            
            itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (onItemClickListener != null) {
                        int position = getAdapterPosition();
                        if (position != RecyclerView.NO_POSITION) {
                            onItemClickListener.onItemClick(caregivers.get(position));
                        }
                    }
                }
            });
        }
        
        public void bind(Caregiver caregiver) {
            tvName.setText(caregiver.getName());
            tvTitle.setText(caregiver.getTitle());
            tvLevel.setText(caregiver.getLevel());
            tvQuote.setText("\"" + caregiver.getQuote() + "\"");
            
            // 设置状态显示
            String status = caregiver.getStatus();
            if ("available".equals(status)) {
                tvStatus.setText("空闲");
                tvStatus.setTextColor(context.getResources().getColor(R.color.green));
            } else if ("busy".equals(status)) {
                tvStatus.setText("忙碌");
                tvStatus.setTextColor(context.getResources().getColor(R.color.orange));
            } else {
                tvStatus.setText("离线");
                tvStatus.setTextColor(context.getResources().getColor(R.color.gray));
            }
            
            // 设置评分
            ratingBar.setRating((float) caregiver.getRating());
            tvRating.setText(String.format("%.1f", caregiver.getRating()));
            tvReviewCount.setText("(" + caregiver.getReviewCount() + "条评价)");
            
            // 设置等级背景色
            if ("高级".equals(caregiver.getLevel())) {
                tvLevel.setBackgroundResource(R.drawable.bg_blue_badge);
            } else if ("中级".equals(caregiver.getLevel())) {
                tvLevel.setBackgroundResource(R.drawable.bg_yellow_badge);
            } else {
                tvLevel.setBackgroundResource(R.drawable.bg_gray_badge);
            }
        }
    }
}
