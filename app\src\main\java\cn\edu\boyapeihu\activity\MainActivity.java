package cn.edu.boyapeihu.activity;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import cn.edu.boyapeihu.R;
import cn.edu.boyapeihu.utils.Constants;
import cn.edu.boyapeihu.utils.SessionManager;

public class MainActivity extends AppCompatActivity {
    
    private Button btnLearnMore;
    private LinearLayout btnMedicalCare, btnElderlyCare, btnHomeCare, btnMaternalCare;
    private LinearLayout navHome, navCaregiver, navPublish, navOrder, navProfile;
    private TextView tvUserName, tvUserRole;
    private View ivMessage;
    private LinearLayout quickSchedule, quickMessage;
    private LinearLayout caregiverCard1, caregiverCard2;

    private SessionManager sessionManager;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        initViews();
        initData();
        setupClickListeners();
    }
    
    private void initViews() {
        btnLearnMore = findViewById(R.id.btn_learn_more);

        // 用户信息显示
        tvUserName = findViewById(R.id.tv_user_name);
        tvUserRole = findViewById(R.id.tv_user_role);
        ivMessage = findViewById(R.id.iv_message);

        // 服务分类按钮
        btnMedicalCare = findViewById(R.id.btn_medical_care);
        btnElderlyCare = findViewById(R.id.btn_elderly_care);
        btnHomeCare = findViewById(R.id.btn_home_care);
        btnMaternalCare = findViewById(R.id.btn_maternal_care);

        // 快捷功能按钮
        quickSchedule = findViewById(R.id.quick_schedule);
        quickMessage = findViewById(R.id.quick_message);

        // 底部导航
        navHome = findViewById(R.id.nav_home);
        navCaregiver = findViewById(R.id.nav_caregiver);
        navPublish = findViewById(R.id.nav_publish);
        navOrder = findViewById(R.id.nav_order);
        navProfile = findViewById(R.id.nav_profile);

        // 护理员风采卡片
        caregiverCard1 = findViewById(R.id.caregiver_card_1);
        caregiverCard2 = findViewById(R.id.caregiver_card_2);
    }
    
    private void initData() {
        sessionManager = new SessionManager(this);

        // 检查登录状态
        if (!sessionManager.isLoggedIn()) {
            startActivity(new Intent(this, LoginActivity.class));
            finish();
            return;
        }

        // 根据角色设置功能按钮的可见性
        setupRoleBasedUI();

        // 显示用户信息
        displayUserInfo();
    }

    /**
     * 根据用户角色设置UI元素的可见性
     */
    private void setupRoleBasedUI() {
        String userRole = sessionManager.getCurrentUserRole();

        switch (userRole) {
            case Constants.ROLE_NURSE:
                // 医护人员：可以访问所有功能
                btnMedicalCare.setVisibility(View.VISIBLE);  // 排班管理
                btnElderlyCare.setVisibility(View.VISIBLE);   // 缴费查看
                btnHomeCare.setVisibility(View.VISIBLE);      // 服务记录查看
                btnMaternalCare.setVisibility(View.VISIBLE);  // 质量监控
                break;

            case Constants.ROLE_ATTENDANT:
                // 陪护人员：主要关注排班和服务记录
                btnMedicalCare.setVisibility(View.VISIBLE);   // 排班查看
                btnElderlyCare.setVisibility(View.VISIBLE);   // 缴费查看
                btnHomeCare.setVisibility(View.VISIBLE);      // 服务记录填写
                btnMaternalCare.setVisibility(View.GONE);     // 不能访问质量监控
                break;

            case Constants.ROLE_PATIENT:
                // 患者：主要关注缴费和服务记录查看
                btnMedicalCare.setVisibility(View.VISIBLE);   // 排班查看
                btnElderlyCare.setVisibility(View.VISIBLE);   // 缴费管理
                btnHomeCare.setVisibility(View.VISIBLE);      // 服务记录查看
                btnMaternalCare.setVisibility(View.GONE);     // 不能访问质量监控
                break;

            default:
                // 未知角色：隐藏所有功能
                btnMedicalCare.setVisibility(View.GONE);
                btnElderlyCare.setVisibility(View.GONE);
                btnHomeCare.setVisibility(View.GONE);
                btnMaternalCare.setVisibility(View.GONE);
                break;
        }
    }

    /**
     * 显示用户信息
     */
    private void displayUserInfo() {
        String userName = sessionManager.getCurrentUserName();
        String userRole = sessionManager.getCurrentUserRole();
        String roleDisplayName = sessionManager.getCurrentUserRoleDisplayName();

        tvUserName.setText(userName);
        tvUserRole.setText(roleDisplayName);
    }
    
    private void setupClickListeners() {
        // 消息按钮
        ivMessage.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(MainActivity.this, MessageActivity.class);
                startActivity(intent);
            }
        });

        // 了解一下按钮
        btnLearnMore.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Toast.makeText(MainActivity.this, "了解更多服务详情", Toast.LENGTH_SHORT).show();
            }
        });

        // 快捷功能按钮
        quickSchedule.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(MainActivity.this, ScheduleActivity.class);
                startActivity(intent);
            }
        });

        quickMessage.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(MainActivity.this, MessageActivity.class);
                startActivity(intent);
            }
        });

        // 服务分类按钮
        btnMedicalCare.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 跳转到排班管理页面
                Intent intent = new Intent(MainActivity.this, ScheduleActivity.class);
                startActivity(intent);
            }
        });

        btnElderlyCare.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 跳转到缴费管理页面
                Intent intent = new Intent(MainActivity.this, PaymentActivity.class);
                startActivity(intent);
            }
        });

        btnHomeCare.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 跳转到服务记录页面
                Intent intent = new Intent(MainActivity.this, ServiceRecordActivity.class);
                startActivity(intent);
            }
        });

        btnMaternalCare.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 跳转到质量监控页面
                Intent intent = new Intent(MainActivity.this, QualityMonitorActivity.class);
                startActivity(intent);
            }
        });
        
        // 底部导航
        navHome.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 已经在首页，不需要跳转
                Toast.makeText(MainActivity.this, "首页", Toast.LENGTH_SHORT).show();
            }
        });
        
        navCaregiver.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 跳转到消息页面（作为沟通功能的入口）
                Intent intent = new Intent(MainActivity.this, MessageActivity.class);
                startActivity(intent);
            }
        });

        navPublish.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 根据角色跳转到不同的发布页面
                String userRole = sessionManager.getCurrentUserRole();
                if (Constants.ROLE_NURSE.equals(userRole)) {
                    // 医护人员：跳转到添加排班页面
                    Intent intent = new Intent(MainActivity.this, AddScheduleActivity.class);
                    startActivity(intent);
                } else if (Constants.ROLE_ATTENDANT.equals(userRole)) {
                    // 陪护人员：跳转到添加服务记录页面
                    Intent intent = new Intent(MainActivity.this, AddServiceRecordActivity.class);
                    startActivity(intent);
                } else if (Constants.ROLE_PATIENT.equals(userRole)) {
                    // 患者：跳转到添加缴费记录页面
                    Intent intent = new Intent(MainActivity.this, AddPaymentActivity.class);
                    startActivity(intent);
                }
            }
        });

        navOrder.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 跳转到排班管理页面（作为订单管理的入口）
                Intent intent = new Intent(MainActivity.this, ScheduleActivity.class);
                startActivity(intent);
            }
        });
        
        navProfile.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(MainActivity.this, ProfileActivity.class);
                startActivity(intent);
            }
        });

        // 护理员风采卡片点击事件
        caregiverCard1.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(MainActivity.this, CaregiverListActivity.class);
                startActivity(intent);
            }
        });

        caregiverCard2.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent(MainActivity.this, CaregiverListActivity.class);
                startActivity(intent);
            }
        });
    }
    
    @Override
    public void onBackPressed() {
        // 在首页按返回键时退出应用
        finish();
    }
} 