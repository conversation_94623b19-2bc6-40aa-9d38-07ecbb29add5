package cn.edu.boyapeihu.entity;

import androidx.room.Entity;
import androidx.room.PrimaryKey;

@Entity(tableName = "caregivers")
public class Caregiver {
    @PrimaryKey(autoGenerate = true)
    private int id;
    
    private String name;
    private String title;
    private String level;
    private String quote;
    private String description;
    private String experience;
    private String specialties;
    private String education;
    private String certifications;
    private String workYears;
    private String phone;
    private String email;
    private double rating;
    private int reviewCount;
    private String avatarUrl;
    private String status; // "available", "busy", "offline"
    
    // 构造函数
    public Caregiver() {}
    
    public Caregiver(String name, String title, String level, String quote, String description, 
                    String experience, String specialties, String education, String certifications,
                    String workYears, String phone, String email, double rating, int reviewCount,
                    String avatarUrl, String status) {
        this.name = name;
        this.title = title;
        this.level = level;
        this.quote = quote;
        this.description = description;
        this.experience = experience;
        this.specialties = specialties;
        this.education = education;
        this.certifications = certifications;
        this.workYears = workYears;
        this.phone = phone;
        this.email = email;
        this.rating = rating;
        this.reviewCount = reviewCount;
        this.avatarUrl = avatarUrl;
        this.status = status;
    }
    
    // Getters and Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getLevel() {
        return level;
    }
    
    public void setLevel(String level) {
        this.level = level;
    }
    
    public String getQuote() {
        return quote;
    }
    
    public void setQuote(String quote) {
        this.quote = quote;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getExperience() {
        return experience;
    }
    
    public void setExperience(String experience) {
        this.experience = experience;
    }
    
    public String getSpecialties() {
        return specialties;
    }
    
    public void setSpecialties(String specialties) {
        this.specialties = specialties;
    }
    
    public String getEducation() {
        return education;
    }
    
    public void setEducation(String education) {
        this.education = education;
    }
    
    public String getCertifications() {
        return certifications;
    }
    
    public void setCertifications(String certifications) {
        this.certifications = certifications;
    }
    
    public String getWorkYears() {
        return workYears;
    }
    
    public void setWorkYears(String workYears) {
        this.workYears = workYears;
    }
    
    public String getPhone() {
        return phone;
    }
    
    public void setPhone(String phone) {
        this.phone = phone;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public double getRating() {
        return rating;
    }
    
    public void setRating(double rating) {
        this.rating = rating;
    }
    
    public int getReviewCount() {
        return reviewCount;
    }
    
    public void setReviewCount(int reviewCount) {
        this.reviewCount = reviewCount;
    }
    
    public String getAvatarUrl() {
        return avatarUrl;
    }
    
    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
}
