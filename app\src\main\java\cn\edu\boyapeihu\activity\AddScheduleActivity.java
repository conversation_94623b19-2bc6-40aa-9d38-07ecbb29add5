package cn.edu.boyapeihu.activity;

import android.app.DatePickerDialog;
import android.app.TimePickerDialog;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.MenuItem;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TimePicker;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.lifecycle.Observer;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import cn.edu.boyapeihu.R;
import cn.edu.boyapeihu.database.AppDatabase;
import cn.edu.boyapeihu.entity.Schedule;
import cn.edu.boyapeihu.entity.User;
import cn.edu.boyapeihu.utils.Constants;
import cn.edu.boyapeihu.utils.DateTimeUtils;
import cn.edu.boyapeihu.utils.SessionManager;

public class AddScheduleActivity extends AppCompatActivity {
    
    private EditText etStartDate, etStartTime, etEndDate, etEndTime;
    private Spinner spinnerAttendant, spinnerPatient;
    private Button btnSave, btnCancel;
    
    private AppDatabase database;
    private SessionManager sessionManager;
    private ExecutorService executor;
    
    private List<User> attendantList;
    private List<User> patientList;
    private ArrayAdapter<String> attendantAdapter;
    private ArrayAdapter<String> patientAdapter;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_add_schedule);
        
        initViews();
        initData();
        loadUsers();
        setListeners();
    }
    
    private void initViews() {
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle("添加排班");
        
        etStartDate = findViewById(R.id.et_start_date);
        etStartTime = findViewById(R.id.et_start_time);
        etEndDate = findViewById(R.id.et_end_date);
        etEndTime = findViewById(R.id.et_end_time);
        spinnerAttendant = findViewById(R.id.spinner_attendant);
        spinnerPatient = findViewById(R.id.spinner_patient);
        btnSave = findViewById(R.id.btn_save);
        btnCancel = findViewById(R.id.btn_cancel);
        
        // 设置默认时间
        setDefaultDateTime();
    }
    
    private void initData() {
        database = AppDatabase.getDatabase(this);
        sessionManager = new SessionManager(this);
        executor = Executors.newSingleThreadExecutor();
        
        attendantList = new ArrayList<>();
        patientList = new ArrayList<>();

        attendantAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_dropdown_item, new ArrayList<String>());
        patientAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_dropdown_item, new ArrayList<String>());

        spinnerAttendant.setAdapter(attendantAdapter);
        spinnerPatient.setAdapter(patientAdapter);
    }
    
    private void setDefaultDateTime() {
        String currentDate = DateTimeUtils.getCurrentDate();
        etStartDate.setText(currentDate);
        etEndDate.setText(currentDate);
        etStartTime.setText("08:00");
        etEndTime.setText("18:00");
    }
    
    private void loadUsers() {
        // 加载陪护人员
        database.userDao().getUsersByRole(Constants.ROLE_ATTENDANT).observe(this, new Observer<List<User>>() {
            @Override
            public void onChanged(List<User> users) {
                attendantList.clear();
                attendantAdapter.clear();
                
                for (User user : users) {
                    attendantList.add(user);
                    attendantAdapter.add(user.getName());
                }
                attendantAdapter.notifyDataSetChanged();
            }
        });
        
        // 加载患者
        database.userDao().getUsersByRole(Constants.ROLE_PATIENT).observe(this, new Observer<List<User>>() {
            @Override
            public void onChanged(List<User> users) {
                patientList.clear();
                patientAdapter.clear();
                
                for (User user : users) {
                    patientList.add(user);
                    patientAdapter.add(user.getName());
                }
                patientAdapter.notifyDataSetChanged();
            }
        });
    }
    
    private void setListeners() {
        // 开始日期选择
        etStartDate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showDatePicker(etStartDate);
            }
        });
        
        // 结束日期选择
        etEndDate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showDatePicker(etEndDate);
            }
        });
        
        // 开始时间选择
        etStartTime.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showTimePicker(etStartTime);
            }
        });
        
        // 结束时间选择
        etEndTime.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showTimePicker(etEndTime);
            }
        });
        
        // 取消按钮
        btnCancel.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        
        // 保存按钮
        btnSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                saveSchedule();
            }
        });
    }
    
    private void showDatePicker(EditText editText) {
        Calendar calendar = Calendar.getInstance();
        DatePickerDialog dialog = new DatePickerDialog(this,
                new DatePickerDialog.OnDateSetListener() {
                    @Override
                    public void onDateSet(DatePicker view, int year, int month, int dayOfMonth) {
                        String date = String.format("%04d-%02d-%02d", year, month + 1, dayOfMonth);
                        editText.setText(date);
                    }
                },
                calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH));
        dialog.show();
    }
    
    private void showTimePicker(EditText editText) {
        Calendar calendar = Calendar.getInstance();
        TimePickerDialog dialog = new TimePickerDialog(this,
                new TimePickerDialog.OnTimeSetListener() {
                    @Override
                    public void onTimeSet(TimePicker view, int hourOfDay, int minute) {
                        String time = String.format("%02d:%02d", hourOfDay, minute);
                        editText.setText(time);
                    }
                },
                calendar.get(Calendar.HOUR_OF_DAY),
                calendar.get(Calendar.MINUTE),
                true);
        dialog.show();
    }
    
    private void saveSchedule() {
        String startDate = etStartDate.getText().toString().trim();
        String startTime = etStartTime.getText().toString().trim();
        String endDate = etEndDate.getText().toString().trim();
        String endTime = etEndTime.getText().toString().trim();
        
        if (TextUtils.isEmpty(startDate) || TextUtils.isEmpty(startTime) ||
            TextUtils.isEmpty(endDate) || TextUtils.isEmpty(endTime)) {
            Toast.makeText(this, "请填写完整的时间信息", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (spinnerAttendant.getSelectedItemPosition() < 0) {
            Toast.makeText(this, "请选择陪护人员", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (spinnerPatient.getSelectedItemPosition() < 0) {
            Toast.makeText(this, "请选择患者", Toast.LENGTH_SHORT).show();
            return;
        }
        
        String startDateTime = startDate + " " + startTime + ":00";
        String endDateTime = endDate + " " + endTime + ":00";
        
        // 验证时间合理性
        if (DateTimeUtils.compareDateTime(startDateTime, endDateTime) >= 0) {
            Toast.makeText(this, "结束时间必须晚于开始时间", Toast.LENGTH_SHORT).show();
            return;
        }
        
        User selectedAttendant = attendantList.get(spinnerAttendant.getSelectedItemPosition());
        User selectedPatient = patientList.get(spinnerPatient.getSelectedItemPosition());
        
        btnSave.setEnabled(false);
        btnSave.setText("保存中...");
        
        executor.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    Schedule schedule = new Schedule(
                            selectedAttendant.getId(),
                            selectedPatient.getId(),
                            startDateTime,
                            endDateTime
                    );
                    schedule.setNurse_id(sessionManager.getCurrentUserId());
                    
                    long result = database.scheduleDao().insert(schedule);
                    
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            btnSave.setEnabled(true);
                            btnSave.setText("保存");
                            
                            if (result > 0) {
                                Toast.makeText(AddScheduleActivity.this, "排班添加成功", Toast.LENGTH_SHORT).show();
                                finish();
                            } else {
                                Toast.makeText(AddScheduleActivity.this, "排班添加失败", Toast.LENGTH_SHORT).show();
                            }
                        }
                    });
                } catch (Exception e) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            btnSave.setEnabled(true);
                            btnSave.setText("保存");
                            Toast.makeText(AddScheduleActivity.this, "保存失败：" + e.getMessage(), Toast.LENGTH_SHORT).show();
                        }
                    });
                }
            }
        });
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }
} 