设计一个适用于 Android 单机 App 的「陪护管理系统」，包括：

角色划分：医护、陪护、患者

核心功能：电子排班、缴费、服务记录、质量监控、三方沟通

一、数据库表结构设计（SQLite）
以 SQLite 为基础，设计如下核心数据表：

1. user 用户表
sql
复制
编辑
CREATE TABLE user (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    role TEXT CHECK(role IN ('nurse', 'attendant', 'patient')) NOT NULL,
    phone TEXT,
    password TEXT,
    avatar TEXT
);
说明：

role 有三个值：nurse（医护）、attendant（陪护）、patient（患者）

avatar：头像路径（可选）

2. schedule 排班表（医护为陪护排班）
sql
复制
编辑
CREATE TABLE schedule (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    attendant_id INTEGER NOT NULL,
    patient_id INTEGER NOT NULL,
    nurse_id INTEGER,
    start_time TEXT NOT NULL,
    end_time TEXT NOT NULL,
    status TEXT CHECK(status IN ('scheduled', 'completed', 'cancelled')) DEFAULT 'scheduled',
    FOREIGN KEY (attendant_id) REFERENCES user(id),
    FOREIGN KEY (patient_id) REFERENCES user(id),
    FOREIGN KEY (nurse_id) REFERENCES user(id)
);
3. payment 缴费记录表（患者向平台或陪护缴费）
sql
复制
编辑
CREATE TABLE payment (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    patient_id INTEGER NOT NULL,
    attendant_id INTEGER NOT NULL,
    amount REAL NOT NULL,
    time TEXT NOT NULL,
    method TEXT, -- 如: cash, wechat, alipay
    remark TEXT,
    FOREIGN KEY (patient_id) REFERENCES user(id),
    FOREIGN KEY (attendant_id) REFERENCES user(id)
);
4. service_record 服务记录表（陪护填写，医护可查）
sql
复制
编辑
CREATE TABLE service_record (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    attendant_id INTEGER NOT NULL,
    patient_id INTEGER NOT NULL,
    time TEXT NOT NULL,
    content TEXT NOT NULL,
    rating INTEGER, -- 满意度评价（患者或医护打分）
    FOREIGN KEY (attendant_id) REFERENCES user(id),
    FOREIGN KEY (patient_id) REFERENCES user(id)
);
5. quality_monitor 质量监控记录（医护填写）
sql
复制
编辑
CREATE TABLE quality_monitor (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    nurse_id INTEGER NOT NULL,
    attendant_id INTEGER NOT NULL,
    patient_id INTEGER,
    time TEXT NOT NULL,
    score INTEGER NOT NULL,
    comment TEXT,
    FOREIGN KEY (nurse_id) REFERENCES user(id),
    FOREIGN KEY (attendant_id) REFERENCES user(id),
    FOREIGN KEY (patient_id) REFERENCES user(id)
);
6. message 消息/沟通记录表（医护/陪护/患者互发消息）
sql
复制
编辑
CREATE TABLE message (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    sender_id INTEGER NOT NULL,
    receiver_id INTEGER NOT NULL,
    time TEXT NOT NULL,
    content TEXT NOT NULL,
    is_read INTEGER DEFAULT 0,
    FOREIGN KEY (sender_id) REFERENCES user(id),
    FOREIGN KEY (receiver_id) REFERENCES user(id)
);
二、功能设计（按角色划分）
👩‍⚕️ 医护功能
【陪护管理】

陪护人员信息查看、编辑、添加、停用

【排班管理】

为患者安排陪护

查看所有排班（过滤时间、陪护、患者）

【服务记录查看】

查看某陪护的所有服务记录（筛选时间）

【质量监控】

提交质量评分

生成陪护服务质量报表

【沟通】

与陪护、患者消息沟通

🧑‍🦯 陪护功能
【我的排班】

查看接下来的排班任务（按日、周、月）

【服务记录】

填写服务内容

查看自己服务历史

【缴费管理】

查看患者支付记录（仅涉及自己）

【沟通】

与医护、患者聊天沟通

【个人资料】

修改手机号、头像、密码

🧑‍🦽 患者功能
【我的陪护】

查看当前/历史陪护人员信息

【缴费】

向陪护/系统支付费用

【服务评价】

对陪护服务评分

【沟通】

与陪护、医护沟通

【个人资料】

编辑基础信息