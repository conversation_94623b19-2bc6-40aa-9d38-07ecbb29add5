<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:cardBackgroundColor="@color/cardBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 缴费时间和状态 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/tv_payment_time"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="缴费时间"
                android:textSize="14sp"
                android:textColor="@color/textColorSecondary"
                android:drawableStart="@drawable/ic_time"
                android:drawablePadding="8dp" />

            <TextView
                android:id="@+id/tv_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="已支付"
                android:textSize="14sp"
                android:textStyle="bold"
                android:background="@drawable/bg_status_pill"
                android:padding="8dp" />

        </LinearLayout>

        <!-- 人员信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/tv_patient_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="患者: 李四"
                android:textSize="16sp"
                android:textColor="@color/textColorPrimary"
                android:textStyle="bold"
                android:drawableStart="@drawable/ic_patient"
                android:drawablePadding="8dp" />

            <TextView
                android:id="@+id/tv_attendant_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="陪护: 张三"
                android:textSize="16sp"
                android:textColor="@color/textColorPrimary"
                android:textStyle="bold"
                android:drawableStart="@drawable/ic_person"
                android:drawablePadding="8dp"
                android:visibility="gone" />

        </LinearLayout>

        <!-- 金额和类型 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/tv_amount"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="¥188.00"
                android:textSize="20sp"
                android:textColor="@color/colorPrimary"
                android:textStyle="bold"
                android:drawableStart="@drawable/ic_payment"
                android:drawablePadding="8dp" />

            <TextView
                android:id="@+id/tv_payment_type"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="护理费"
                android:textSize="16sp"
                android:textColor="@color/textColorPrimary"
                android:textStyle="bold"
                android:gravity="end" />

        </LinearLayout>

        <!-- 描述 -->
        <TextView
            android:id="@+id/tv_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="费用描述详情"
            android:textSize="14sp"
            android:textColor="@color/textColorSecondary"
            android:maxLines="2"
            android:ellipsize="end"
            android:visibility="gone" />

    </LinearLayout>

</androidx.cardview.widget.CardView> 