# 首页设计说明

## 设计概述
根据提供的设计图，我们重新设计了陪护管理系统的首页，采用了现代化的UI设计，主要包含以下元素：

## 主要功能区域

### 1. 顶部标题栏
- 显示"您身边的网约护理"标题
- 采用简洁的黑色文字设计

### 2. 健康私人订制横幅
- 使用青色渐变背景
- 左侧显示"健康私人订制"和"关爱生命 守护健康"文字
- 包含"了解一下"按钮
- 右侧显示医生图标
- 右上角有通知铃铛图标

### 3. 服务通知栏
- 浅灰色背景
- 左侧扬声器图标
- 显示服务通知信息
- 右侧箭头指示更多信息

### 4. 服务分类网格（2x2布局）
- **医疗护理**：蓝色渐变背景，医疗图标
- **养老护理**：青色背景，养老图标
- **长期或居家**：橙色渐变背景，日历时钟图标
- **母婴护理**：粉色渐变背景，母婴图标

### 5. 护理员风采
- 蓝色竖线标题装饰
- 两个护理员卡片并排显示
- 每个卡片包含：
  - 护理员头像（占位图标）
  - 等级徽章（高级/中级）
  - 个人格言（橙色文字）
  - 姓名和职称

### 6. 底部导航栏
- 白色背景，圆角设计
- 五个导航项：
  - **首页**：青色高亮（当前页面）
  - **护理员**：灰色图标和文字
  - **发布**：圆形青色按钮，白色加号图标
  - **订单**：灰色图标和文字
  - **我的**：灰色图标和文字

## 颜色方案
- **主色调**：青色 (#00838F)
- **辅助色**：蓝色 (#2196F3)
- **渐变色**：橙色渐变、粉色渐变
- **文字色**：主要文字黑色，次要文字灰色
- **背景色**：浅灰色背景

## 交互功能
- 所有按钮和卡片都有点击反馈
- 服务分类按钮显示Toast提示
- 底部导航项显示Toast提示
- "了解一下"按钮显示服务详情提示
- 个人资料按钮跳转到ProfileActivity

## 技术实现
- 使用RelativeLayout作为根布局
- ScrollView支持内容滚动
- 自定义drawable资源实现渐变和圆角效果
- 响应式设计适配不同屏幕尺寸
- 遵循Material Design设计规范

## 文件结构
- `activity_main.xml`：主布局文件
- `MainActivity.java`：主Activity逻辑
- `colors.xml`：颜色资源定义
- `strings.xml`：文本资源定义
- `drawable/`：各种背景和图标资源

这个设计完全参考了提供的设计图，实现了现代化的护理服务应用界面。 