package cn.edu.boyapeihu.entity;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.ForeignKey;
import androidx.room.Ignore;

@Entity(tableName = "message",
        foreignKeys = {
            @ForeignKey(entity = User.class, parentColumns = "id", childColumns = "sender_id"),
            @ForeignKey(entity = User.class, parentColumns = "id", childColumns = "receiver_id")
        })
public class Message {
    @PrimaryKey(autoGenerate = true)
    private int id;
    
    private int sender_id;
    private int receiver_id;
    private String time;
    private String content;
    private int is_read; // 0-未读，1-已读
    
    // 构造函数
    public Message() {}
    
    @Ignore
    public Message(int sender_id, int receiver_id, String time, String content) {
        this.sender_id = sender_id;
        this.receiver_id = receiver_id;
        this.time = time;
        this.content = content;
        this.is_read = 0; // 默认未读
    }
    
    // Getter和Setter方法
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public int getSender_id() {
        return sender_id;
    }
    
    public void setSender_id(int sender_id) {
        this.sender_id = sender_id;
    }
    
    public int getReceiver_id() {
        return receiver_id;
    }
    
    public void setReceiver_id(int receiver_id) {
        this.receiver_id = receiver_id;
    }
    
    public String getTime() {
        return time;
    }
    
    public void setTime(String time) {
        this.time = time;
    }
    
    public String getSend_time() {
        return time;
    }
    
    public void setSend_time(String send_time) {
        this.time = send_time;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public int getIs_read() {
        return is_read;
    }
    
    public void setIs_read(int is_read) {
        this.is_read = is_read;
    }
    
    // 阅读状态常量
    public static final int UNREAD = 0;
    public static final int READ = 1;
} 