package cn.edu.boyapeihu.adapter;

import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import cn.edu.boyapeihu.R;
import cn.edu.boyapeihu.database.AppDatabase;
import cn.edu.boyapeihu.entity.Payment;
import cn.edu.boyapeihu.entity.User;
import cn.edu.boyapeihu.utils.DateTimeUtils;

public class PaymentAdapter extends RecyclerView.Adapter<PaymentAdapter.PaymentViewHolder> {
    
    private Context context;
    private List<Payment> payments;
    private AppDatabase database;
    private ExecutorService executor;
    
    public PaymentAdapter(Context context) {
        this.context = context;
        this.payments = new ArrayList<>();
        this.database = AppDatabase.getDatabase(context);
        this.executor = Executors.newSingleThreadExecutor();
    }
    
    @NonNull
    @Override
    public PaymentViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_payment, parent, false);
        return new PaymentViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull PaymentViewHolder holder, int position) {
        Payment payment = payments.get(position);
        holder.bind(payment);
    }
    
    @Override
    public int getItemCount() {
        return payments.size();
    }
    
    public void setPayments(List<Payment> payments) {
        this.payments = payments;
        notifyDataSetChanged();
    }
    
    class PaymentViewHolder extends RecyclerView.ViewHolder {
        
        private TextView tvAttendantName, tvPatientName, tvPaymentTime, tvAmount, tvPaymentType, tvStatus, tvDescription;
        
        public PaymentViewHolder(@NonNull View itemView) {
            super(itemView);
            
            tvAttendantName = itemView.findViewById(R.id.tv_attendant_name);
            tvPatientName = itemView.findViewById(R.id.tv_patient_name);
            tvPaymentTime = itemView.findViewById(R.id.tv_payment_time);
            tvAmount = itemView.findViewById(R.id.tv_amount);
            tvPaymentType = itemView.findViewById(R.id.tv_payment_type);
            tvStatus = itemView.findViewById(R.id.tv_status);
            tvDescription = itemView.findViewById(R.id.tv_description);
        }
        
        public void bind(Payment payment) {
            // 设置缴费时间
            tvPaymentTime.setText(DateTimeUtils.formatDateTime(payment.getPayment_time()));
            
            // 设置金额
            tvAmount.setText("¥" + String.format("%.2f", payment.getAmount()));
            
            // 设置缴费类型
            tvPaymentType.setText(payment.getPayment_type());
            
            // 设置状态
            tvStatus.setText(payment.getStatus());
            
            // 根据状态设置不同颜色
            switch (payment.getStatus()) {
                case "已支付":
                    tvStatus.setTextColor(Color.parseColor("#4CAF50"));
                    break;
                case "待支付":
                    tvStatus.setTextColor(Color.parseColor("#FF9800"));
                    break;
                case "已退款":
                    tvStatus.setTextColor(Color.parseColor("#F44336"));
                    break;
                default:
                    tvStatus.setTextColor(Color.parseColor("#757575"));
                    break;
            }
            
            // 设置描述
            if (payment.getDescription() != null && !payment.getDescription().trim().isEmpty()) {
                tvDescription.setText(payment.getDescription());
                tvDescription.setVisibility(View.VISIBLE);
            } else {
                tvDescription.setVisibility(View.GONE);
            }
            
            // 异步加载用户名称
            loadUserNames(payment);
        }
        
        private void loadUserNames(Payment payment) {
            executor.execute(new Runnable() {
                @Override
                public void run() {
                    // 获取患者姓名
                    User patient = database.userDao().getUserByIdSync(payment.getPatient_id());
                    // 获取陪护人员姓名
                    User attendant = null;
                    if (payment.getAttendant_id() > 0) {
                        attendant = database.userDao().getUserByIdSync(payment.getAttendant_id());
                    }
                    
                    final User finalAttendant = attendant;
                    
                    // 更新UI
                    if (itemView.getContext() instanceof android.app.Activity) {
                        ((android.app.Activity) itemView.getContext()).runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                if (patient != null) {
                                    tvPatientName.setText("患者: " + patient.getName());
                                }
                                if (finalAttendant != null) {
                                    tvAttendantName.setText("陪护: " + finalAttendant.getName());
                                    tvAttendantName.setVisibility(View.VISIBLE);
                                } else {
                                    tvAttendantName.setVisibility(View.GONE);
                                }
                            }
                        });
                    }
                }
            });
        }
    }
} 