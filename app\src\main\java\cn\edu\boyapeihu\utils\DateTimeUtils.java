package cn.edu.boyapeihu.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

public class DateTimeUtils {
    
    // 日期时间格式常量
    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_FORMAT = "yyyy-MM-dd";
    public static final String TIME_FORMAT = "HH:mm";
    public static final String DISPLAY_DATE_TIME_FORMAT = "MM月dd日 HH:mm";
    public static final String DISPLAY_DATE_FORMAT = "MM月dd日";
    
    private static final SimpleDateFormat dateTimeFormatter = new SimpleDateFormat(DATE_TIME_FORMAT, Locale.getDefault());
    private static final SimpleDateFormat dateFormatter = new SimpleDateFormat(DATE_FORMAT, Locale.getDefault());
    private static final SimpleDateFormat timeFormatter = new SimpleDateFormat(TIME_FORMAT, Locale.getDefault());
    private static final SimpleDateFormat displayDateTimeFormatter = new SimpleDateFormat(DISPLAY_DATE_TIME_FORMAT, Locale.getDefault());
    private static final SimpleDateFormat displayDateFormatter = new SimpleDateFormat(DISPLAY_DATE_FORMAT, Locale.getDefault());
    
    /**
     * 获取当前日期时间字符串
     */
    public static String getCurrentDateTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
        return sdf.format(new Date());
    }
    
    /**
     * 获取当前日期字符串
     */
    public static String getCurrentDate() {
        return dateFormatter.format(new Date());
    }
    
    /**
     * 获取当前时间字符串
     */
    public static String getCurrentTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm", Locale.getDefault());
        return sdf.format(new Date());
    }
    
    /**
     * 日期时间字符串转换为显示格式
     */
    public static String formatDisplayDateTime(String dateTimeStr) {
        try {
            Date date = dateTimeFormatter.parse(dateTimeStr);
            return displayDateTimeFormatter.format(date);
        } catch (ParseException e) {
            return dateTimeStr;
        }
    }
    
    /**
     * 格式化日期时间字符串
     */
    public static String formatDateTime(String dateTimeStr) {
        try {
            Date date = dateTimeFormatter.parse(dateTimeStr);
            return displayDateTimeFormatter.format(date);
        } catch (ParseException e) {
            return dateTimeStr;
        }
    }
    
    /**
     * 日期字符串转换为显示格式
     */
    public static String formatDisplayDate(String dateStr) {
        try {
            Date date = dateFormatter.parse(dateStr);
            return displayDateFormatter.format(date);
        } catch (ParseException e) {
            return dateStr;
        }
    }
    
    /**
     * 获取指定日期的开始时间（00:00:00）
     */
    public static String getDateStart(String dateStr) {
        return dateStr + " 00:00:00";
    }
    
    /**
     * 获取指定日期的结束时间（23:59:59）
     */
    public static String getDateEnd(String dateStr) {
        return dateStr + " 23:59:59";
    }
    
    /**
     * 获取指定天数前的日期
     */
    public static String getDateBefore(int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -days);
        return dateFormatter.format(calendar.getTime());
    }
    
    /**
     * 获取指定天数后的日期
     */
    public static String getDateAfter(int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, days);
        return dateFormatter.format(calendar.getTime());
    }
    
    /**
     * 比较两个日期时间的大小
     * @return 1: dateTime1 > dateTime2, 0: 相等, -1: dateTime1 < dateTime2
     */
    public static int compareDateTime(String dateTime1, String dateTime2) {
        try {
            Date date1 = dateTimeFormatter.parse(dateTime1);
            Date date2 = dateTimeFormatter.parse(dateTime2);
            return date1.compareTo(date2);
        } catch (ParseException e) {
            return 0;
        }
    }
    
    /**
     * 检查时间字符串是否有效
     */
    public static boolean isValidDateTime(String dateTimeStr) {
        try {
            dateTimeFormatter.parse(dateTimeStr);
            return true;
        } catch (ParseException e) {
            return false;
        }
    }
    
    /**
     * 检查日期字符串是否有效
     */
    public static boolean isValidDate(String dateStr) {
        try {
            dateFormatter.parse(dateStr);
            return true;
        } catch (ParseException e) {
            return false;
        }
    }
} 