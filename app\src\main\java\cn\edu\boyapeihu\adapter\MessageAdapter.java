package cn.edu.boyapeihu.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import cn.edu.boyapeihu.R;
import cn.edu.boyapeihu.database.AppDatabase;
import cn.edu.boyapeihu.entity.Message;
import cn.edu.boyapeihu.entity.User;
import cn.edu.boyapeihu.utils.DateTimeUtils;
import cn.edu.boyapeihu.utils.SessionManager;

public class MessageAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {
    
    private static final int TYPE_SENT = 1;
    private static final int TYPE_RECEIVED = 2;
    
    private Context context;
    private List<Message> messages;
    private AppDatabase database;
    private ExecutorService executor;
    private SessionManager sessionManager;
    private int currentUserId;
    
    public MessageAdapter(Context context) {
        this.context = context;
        this.messages = new ArrayList<>();
        this.database = AppDatabase.getDatabase(context);
        this.executor = Executors.newSingleThreadExecutor();
        this.sessionManager = new SessionManager(context);
        this.currentUserId = sessionManager.getCurrentUserId();
    }
    
    @Override
    public int getItemViewType(int position) {
        Message message = messages.get(position);
        if (message.getSender_id() == currentUserId) {
            return TYPE_SENT;
        } else {
            return TYPE_RECEIVED;
        }
    }
    
    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        if (viewType == TYPE_SENT) {
            View view = LayoutInflater.from(context).inflate(R.layout.item_message_sent, parent, false);
            return new SentMessageViewHolder(view);
        } else {
            View view = LayoutInflater.from(context).inflate(R.layout.item_message_received, parent, false);
            return new ReceivedMessageViewHolder(view);
        }
    }
    
    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        Message message = messages.get(position);
        
        if (holder instanceof SentMessageViewHolder) {
            ((SentMessageViewHolder) holder).bind(message);
        } else if (holder instanceof ReceivedMessageViewHolder) {
            ((ReceivedMessageViewHolder) holder).bind(message);
        }
    }
    
    @Override
    public int getItemCount() {
        return messages.size();
    }
    
    public void setMessages(List<Message> messages) {
        this.messages = messages;
        notifyDataSetChanged();
    }
    
    // 发送消息的ViewHolder
    class SentMessageViewHolder extends RecyclerView.ViewHolder {
        
        private TextView tvMessage, tvTime, tvReceiverName, tvStatus;
        
        public SentMessageViewHolder(@NonNull View itemView) {
            super(itemView);
            
            tvMessage = itemView.findViewById(R.id.tv_message);
            tvTime = itemView.findViewById(R.id.tv_time);
            tvReceiverName = itemView.findViewById(R.id.tv_receiver_name);
            tvStatus = itemView.findViewById(R.id.tv_status);
        }
        
        public void bind(Message message) {
            if (tvMessage != null) {
                tvMessage.setText(message.getContent());
            }
            if (tvTime != null) {
                tvTime.setText(DateTimeUtils.formatDateTime(message.getSend_time()));
            }

            // 设置消息状态
            if (tvStatus != null) {
                if (message.getIs_read() == 1) {
                    tvStatus.setText("已读");
                    tvStatus.setTextColor(context.getResources().getColor(android.R.color.holo_green_dark));
                } else {
                    tvStatus.setText("未读");
                    tvStatus.setTextColor(context.getResources().getColor(android.R.color.holo_orange_dark));
                }
            }

            // 异步加载接收者姓名
            loadReceiverName(message);
        }
        
        private void loadReceiverName(Message message) {
            executor.execute(new Runnable() {
                @Override
                public void run() {
                    User receiver = database.userDao().getUserByIdSync(message.getReceiver_id());
                    
                    if (itemView.getContext() instanceof android.app.Activity) {
                        ((android.app.Activity) itemView.getContext()).runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                if (receiver != null && tvReceiverName != null) {
                                    tvReceiverName.setText("发送给: " + receiver.getName());
                                }
                            }
                        });
                    }
                }
            });
        }
    }
    
    // 接收消息的ViewHolder
    class ReceivedMessageViewHolder extends RecyclerView.ViewHolder {
        
        private TextView tvMessage, tvTime, tvSenderName;
        
        public ReceivedMessageViewHolder(@NonNull View itemView) {
            super(itemView);
            
            tvMessage = itemView.findViewById(R.id.tv_message);
            tvTime = itemView.findViewById(R.id.tv_time);
            tvSenderName = itemView.findViewById(R.id.tv_sender_name);
        }
        
        public void bind(Message message) {
            if (tvMessage != null) {
                tvMessage.setText(message.getContent());
            }
            if (tvTime != null) {
                tvTime.setText(DateTimeUtils.formatDateTime(message.getSend_time()));
            }

            // 异步加载发送者姓名
            loadSenderName(message);
        }
        
        private void loadSenderName(Message message) {
            executor.execute(new Runnable() {
                @Override
                public void run() {
                    User sender = database.userDao().getUserByIdSync(message.getSender_id());
                    
                    if (itemView.getContext() instanceof android.app.Activity) {
                        ((android.app.Activity) itemView.getContext()).runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                if (sender != null && tvSenderName != null) {
                                    tvSenderName.setText(sender.getName());
                                }
                            }
                        });
                    }
                }
            });
        }
    }
} 