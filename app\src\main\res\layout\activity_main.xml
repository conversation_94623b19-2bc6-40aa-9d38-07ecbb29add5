<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/backgroundColor">

    <!-- 顶部标题栏 -->
    <LinearLayout
        android:id="@+id/header_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 用户信息和标题 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="16dp">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/home_title"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/textColorPrimary" />

                <!-- 用户信息显示 -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginTop="4dp">

                    <TextView
                        android:id="@+id/tv_user_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="用户姓名"
                        android:textSize="14sp"
                        android:textColor="@color/textColorSecondary" />

                    <TextView
                        android:id="@+id/tv_user_role"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="角色"
                        android:textSize="12sp"
                        android:textColor="@color/white"
                        android:background="@drawable/bg_teal_solid"
                        android:paddingStart="8dp"
                        android:paddingEnd="8dp"
                        android:paddingTop="2dp"
                        android:paddingBottom="2dp"
                        android:layout_marginStart="8dp" />

                </LinearLayout>

            </LinearLayout>

            <!-- 消息图标 -->
            <ImageView
                android:id="@+id/iv_message"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_message"
                android:tint="@color/teal_primary"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?android:attr/selectableItemBackground"
                android:padding="4dp" />

        </LinearLayout>

        <!-- 健康私人订制横幅 -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="200dp"
            android:background="@drawable/bg_teal_gradient"
            android:padding="20dp">

            <!-- 左侧文字内容 -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/health_customization"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:textColor="@color/white"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/care_life_guard_health"
                    android:textSize="14sp"
                    android:textColor="@color/white"
                    android:layout_marginBottom="16dp" />

                <Button
                    android:id="@+id/btn_learn_more"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/learn_more"
                    android:textColor="@color/teal_primary"
                    android:background="@drawable/bg_white_button"
                    android:paddingStart="20dp"
                    android:paddingEnd="20dp"
                    android:paddingTop="8dp"
                    android:paddingBottom="8dp"
                    android:textSize="14sp" />

            </LinearLayout>

            <!-- 右侧医生图标 -->
            <ImageView
                android:layout_width="80dp"
                android:layout_height="120dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:src="@drawable/ic_person"
                android:tint="@color/white"
                android:background="@drawable/bg_teal_solid"
                android:padding="16dp" />

            <!-- 通知铃铛 -->
            <ImageView
                android:id="@+id/iv_notification"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_alignParentTop="true"
                android:layout_alignParentEnd="true"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="8dp"
                android:src="@drawable/ic_bell"
                android:tint="@color/white" />

        </RelativeLayout>

        <!-- 服务通知栏 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:background="@drawable/bg_notification_bar"
            android:padding="12dp"
            android:layout_marginTop="12dp"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_speaker"
                android:layout_marginEnd="8dp" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/service_notice"
                android:textSize="12sp"
                android:textColor="@color/textColorSecondary"
                android:maxLines="2"
                android:ellipsize="end" />

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_arrow_right" />

        </LinearLayout>

    </LinearLayout>

    <!-- 主要内容区域 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/header_layout"
        android:layout_above="@id/bottom_navigation">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 服务分类网格 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="24dp">

                <!-- 第一行 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="12dp">

                    <!-- 医疗护理 -->
                    <LinearLayout
                        android:id="@+id/btn_medical_care"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_weight="1"
                        android:layout_marginEnd="6dp"
                        android:background="@drawable/bg_blue_gradient"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="16dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:foreground="?android:attr/selectableItemBackground">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:src="@drawable/ic_medical_care"
                            android:tint="@color/white" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/medical_care"
                            android:textSize="14sp"
                            android:textColor="@color/white"
                            android:layout_marginTop="8dp" />

                    </LinearLayout>

                    <!-- 养老护理 -->
                    <LinearLayout
                        android:id="@+id/btn_elderly_care"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_weight="1"
                        android:layout_marginStart="6dp"
                        android:background="@drawable/bg_teal_solid"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="16dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:foreground="?android:attr/selectableItemBackground">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:src="@drawable/ic_elderly_care"
                            android:tint="@color/white" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/elderly_care"
                            android:textSize="14sp"
                            android:textColor="@color/white"
                            android:layout_marginTop="8dp" />

                    </LinearLayout>

                </LinearLayout>

                <!-- 第二行 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <!-- 长期或居家 -->
                    <LinearLayout
                        android:id="@+id/btn_home_care"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_weight="1"
                        android:layout_marginEnd="6dp"
                        android:background="@drawable/bg_orange_gradient"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="16dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:foreground="?android:attr/selectableItemBackground">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:src="@drawable/ic_home_care"
                            android:tint="@color/white" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/home_care"
                            android:textSize="14sp"
                            android:textColor="@color/white"
                            android:layout_marginTop="8dp" />

                    </LinearLayout>

                    <!-- 母婴护理 -->
                    <LinearLayout
                        android:id="@+id/btn_maternal_care"
                        android:layout_width="0dp"
                        android:layout_height="120dp"
                        android:layout_weight="1"
                        android:layout_marginStart="6dp"
                        android:background="@drawable/bg_pink_gradient"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="16dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:foreground="?android:attr/selectableItemBackground">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:src="@drawable/ic_maternal_care"
                            android:tint="@color/white" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/maternal_care"
                            android:textSize="14sp"
                            android:textColor="@color/white"
                            android:layout_marginTop="8dp" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

            <!-- 护理员风采 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- 标题 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <View
                        android:layout_width="4dp"
                        android:layout_height="20dp"
                        android:background="@color/blue_primary"
                        android:layout_marginEnd="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/caregiver_style"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:textColor="@color/textColorPrimary" />

                </LinearLayout>

                <!-- 护理员卡片 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <!-- 护理员1 -->
                    <LinearLayout
                        android:id="@+id/caregiver_card_1"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        android:background="@drawable/bg_caregiver_card"
                        android:orientation="vertical"
                        android:padding="12dp"
                        android:clickable="true"
                        android:focusable="true">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <ImageView
                                android:layout_width="60dp"
                                android:layout_height="60dp"
                                android:src="@drawable/ic_person"
                                android:background="@color/light_gray"
                                android:padding="12dp"
                                android:layout_centerHorizontal="true" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/senior_level"
                                android:textSize="10sp"
                                android:textColor="@color/white"
                                android:background="@drawable/bg_blue_badge"
                                android:paddingStart="6dp"
                                android:paddingEnd="6dp"
                                android:paddingTop="2dp"
                                android:paddingBottom="2dp"
                                android:layout_alignParentEnd="true"
                                android:layout_alignParentTop="true" />

                        </RelativeLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/caregiver_1_quote"
                            android:textSize="12sp"
                            android:textColor="@color/textColorOrange"
                            android:layout_gravity="center"
                            android:layout_marginTop="8dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/caregiver_1_name"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="@color/textColorPrimary"
                            android:layout_gravity="center"
                            android:layout_marginTop="4dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/caregiver_1_title"
                            android:textSize="12sp"
                            android:textColor="@color/textColorSecondary"
                            android:layout_gravity="center"
                            android:layout_marginTop="2dp" />

                    </LinearLayout>

                    <!-- 护理员2 -->
                    <LinearLayout
                        android:id="@+id/caregiver_card_2"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        android:background="@drawable/bg_caregiver_card"
                        android:orientation="vertical"
                        android:padding="12dp"
                        android:clickable="true"
                        android:focusable="true">

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <ImageView
                                android:layout_width="60dp"
                                android:layout_height="60dp"
                                android:src="@drawable/ic_person"
                                android:background="@color/light_gray"
                                android:padding="12dp"
                                android:layout_centerHorizontal="true" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/intermediate_level"
                                android:textSize="10sp"
                                android:textColor="@color/white"
                                android:background="@drawable/bg_yellow_badge"
                                android:paddingStart="6dp"
                                android:paddingEnd="6dp"
                                android:paddingTop="2dp"
                                android:paddingBottom="2dp"
                                android:layout_alignParentEnd="true"
                                android:layout_alignParentTop="true" />

                        </RelativeLayout>

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/caregiver_2_quote"
                            android:textSize="12sp"
                            android:textColor="@color/textColorOrange"
                            android:layout_gravity="center"
                            android:layout_marginTop="8dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/caregiver_2_name"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:textColor="@color/textColorPrimary"
                            android:layout_gravity="center"
                            android:layout_marginTop="4dp" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/caregiver_2_title"
                            android:textSize="12sp"
                            android:textColor="@color/textColorSecondary"
                            android:layout_gravity="center"
                            android:layout_marginTop="2dp" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

            <!-- 快捷功能区域 -->
            <LinearLayout
                android:id="@+id/quick_actions_section"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginTop="24dp">

                <!-- 快捷功能标题 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <View
                        android:layout_width="4dp"
                        android:layout_height="20dp"
                        android:background="@color/teal_primary"
                        android:layout_marginEnd="8dp" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="快捷功能"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/textColorPrimary" />

                </LinearLayout>

                <!-- 快捷功能按钮网格 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <!-- 我的排班 -->
                    <LinearLayout
                        android:id="@+id/quick_schedule"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="16dp"
                        android:background="@drawable/bg_caregiver_card"
                        android:layout_marginEnd="8dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:foreground="?android:attr/selectableItemBackground">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:src="@drawable/ic_schedule"
                            android:tint="@color/teal_primary" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="我的排班"
                            android:textSize="12sp"
                            android:textColor="@color/textColorPrimary"
                            android:layout_marginTop="8dp" />

                    </LinearLayout>

                    <!-- 消息沟通 -->
                    <LinearLayout
                        android:id="@+id/quick_message"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:padding="16dp"
                        android:background="@drawable/bg_caregiver_card"
                        android:layout_marginStart="8dp"
                        android:clickable="true"
                        android:focusable="true"
                        android:foreground="?android:attr/selectableItemBackground">

                        <ImageView
                            android:layout_width="32dp"
                            android:layout_height="32dp"
                            android:src="@drawable/ic_message"
                            android:tint="@color/teal_primary" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="消息沟通"
                            android:textSize="12sp"
                            android:textColor="@color/textColorPrimary"
                            android:layout_marginTop="8dp" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

    <!-- 底部导航栏 -->
    <LinearLayout
        android:id="@+id/bottom_navigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@drawable/bg_bottom_nav"
        android:orientation="horizontal"
        android:padding="8dp"
        android:elevation="8dp">

        <!-- 首页 -->
        <LinearLayout
            android:id="@+id/nav_home"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?android:attr/selectableItemBackground">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_home"
                android:tint="@color/teal_primary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/nav_home"
                android:textSize="10sp"
                android:textColor="@color/teal_primary"
                android:layout_marginTop="2dp" />

        </LinearLayout>

        <!-- 护理员 -->
        <LinearLayout
            android:id="@+id/nav_caregiver"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?android:attr/selectableItemBackground">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_caregiver"
                android:tint="@color/textColorSecondary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/nav_caregiver"
                android:textSize="10sp"
                android:textColor="@color/textColorSecondary"
                android:layout_marginTop="2dp" />

        </LinearLayout>

        <!-- 发布 -->
        <LinearLayout
            android:id="@+id/nav_publish"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?android:attr/selectableItemBackground">

            <ImageView
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:src="@drawable/ic_add"
                android:background="@drawable/bg_publish_button"
                android:padding="4dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/nav_publish"
                android:textSize="10sp"
                android:textColor="@color/textColorSecondary"
                android:layout_marginTop="2dp" />

        </LinearLayout>

        <!-- 订单 -->
        <LinearLayout
            android:id="@+id/nav_order"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?android:attr/selectableItemBackground">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_order"
                android:tint="@color/textColorSecondary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/nav_order"
                android:textSize="10sp"
                android:textColor="@color/textColorSecondary"
                android:layout_marginTop="2dp" />

        </LinearLayout>

        <!-- 我的 -->
        <LinearLayout
            android:id="@+id/nav_profile"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true"
            android:foreground="?android:attr/selectableItemBackground">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_profile"
                android:tint="@color/textColorSecondary" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/nav_profile"
                android:textSize="10sp"
                android:textColor="@color/textColorSecondary"
                android:layout_marginTop="2dp" />

        </LinearLayout>

    </LinearLayout>

</RelativeLayout> 