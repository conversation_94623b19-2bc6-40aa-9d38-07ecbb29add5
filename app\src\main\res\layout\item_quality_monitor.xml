<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:cardBackgroundColor="@color/cardBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 评价时间和综合评分 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/tv_evaluation_time"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="评价时间"
                android:textSize="14sp"
                android:textColor="@color/textColorSecondary"
                android:drawableStart="@drawable/ic_time"
                android:drawablePadding="8dp" />

            <TextView
                android:id="@+id/tv_quality_score"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="综合评分: 5/5"
                android:textSize="16sp"
                android:textStyle="bold"
                android:background="@drawable/bg_status_pill"
                android:padding="8dp" />

        </LinearLayout>

        <!-- 人员信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="4dp">

                <TextView
                    android:id="@+id/tv_attendant_name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="陪护: 张三"
                    android:textSize="16sp"
                    android:textColor="@color/textColorPrimary"
                    android:textStyle="bold"
                    android:drawableStart="@drawable/ic_person"
                    android:drawablePadding="8dp" />

                <TextView
                    android:id="@+id/tv_patient_name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="患者: 李四"
                    android:textSize="16sp"
                    android:textColor="@color/textColorPrimary"
                    android:textStyle="bold"
                    android:drawableStart="@drawable/ic_patient"
                    android:drawablePadding="8dp" />

            </LinearLayout>

            <TextView
                android:id="@+id/tv_nurse_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="评价医护: 王五"
                android:textSize="14sp"
                android:textColor="@color/textColorSecondary"
                android:drawableStart="@drawable/ic_profile"
                android:drawablePadding="8dp" />

        </LinearLayout>

        <!-- 详细评分 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/bg_status_pill"
            android:backgroundTint="#F5F5F5"
            android:padding="12dp"
            android:layout_marginBottom="8dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="详细评分"
                android:textSize="14sp"
                android:textColor="@color/textColorPrimary"
                android:textStyle="bold"
                android:layout_marginBottom="8dp"
                android:drawableStart="@drawable/ic_quality"
                android:drawablePadding="8dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_service_attitude"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="服务态度: 5/5"
                    android:textSize="13sp"
                    android:textColor="@color/textColorSecondary" />

                <TextView
                    android:id="@+id/tv_professional_skill"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="专业技能: 5/5"
                    android:textSize="13sp"
                    android:textColor="@color/textColorSecondary" />

                <TextView
                    android:id="@+id/tv_communication"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="沟通能力: 5/5"
                    android:textSize="13sp"
                    android:textColor="@color/textColorSecondary" />

            </LinearLayout>

        </LinearLayout>

        <!-- 评价意见 -->
        <TextView
            android:id="@+id/tv_comments"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="评价意见详情"
            android:textSize="14sp"
            android:textColor="@color/textColorSecondary"
            android:maxLines="3"
            android:ellipsize="end"
            android:visibility="gone" />

    </LinearLayout>

</androidx.cardview.widget.CardView> 