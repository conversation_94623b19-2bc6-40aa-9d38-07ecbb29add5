package cn.edu.boyapeihu.activity;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.MenuItem;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import cn.edu.boyapeihu.R;
import cn.edu.boyapeihu.database.AppDatabase;
import cn.edu.boyapeihu.entity.User;
import cn.edu.boyapeihu.utils.Constants;
import cn.edu.boyapeihu.utils.CryptoUtils;

public class RegisterActivity extends AppCompatActivity {
    
    private EditText etName, etPhone, etPassword, etConfirmPassword;
    private Spinner spinnerRole;
    private Button btnRegister;
    
    private AppDatabase database;
    private ExecutorService executor;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_register);
        
        initViews();
        initData();
        setListeners();
    }
    
    private void initViews() {
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle("用户注册");
        
        etName = findViewById(R.id.et_name);
        etPhone = findViewById(R.id.et_phone);
        etPassword = findViewById(R.id.et_password);
        etConfirmPassword = findViewById(R.id.et_confirm_password);
        spinnerRole = findViewById(R.id.spinner_role);
        btnRegister = findViewById(R.id.btn_register);
    }
    
    private void initData() {
        database = AppDatabase.getDatabase(this);
        executor = Executors.newSingleThreadExecutor();
        
        // 设置角色选择器
        String[] roles = {"医护人员", "陪护人员", "患者"};
        ArrayAdapter<String> adapter = new ArrayAdapter<>(this, 
                android.R.layout.simple_spinner_dropdown_item, roles);
        spinnerRole.setAdapter(adapter);
    }
    
    private void setListeners() {
        btnRegister.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                register();
            }
        });
    }
    
    private void register() {
        String name = etName.getText().toString().trim();
        String phone = etPhone.getText().toString().trim();
        String password = etPassword.getText().toString().trim();
        String confirmPassword = etConfirmPassword.getText().toString().trim();
        int rolePosition = spinnerRole.getSelectedItemPosition();
        
        // 验证输入
        if (TextUtils.isEmpty(name)) {
            Toast.makeText(this, "请输入姓名", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (TextUtils.isEmpty(phone)) {
            Toast.makeText(this, "请输入手机号", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (phone.length() != 11) {
            Toast.makeText(this, "请输入正确的手机号", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (TextUtils.isEmpty(password)) {
            Toast.makeText(this, "请输入密码", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (password.length() < 6) {
            Toast.makeText(this, "密码长度不能少于6位", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (!password.equals(confirmPassword)) {
            Toast.makeText(this, "两次输入的密码不一致", Toast.LENGTH_SHORT).show();
            return;
        }
        
        // 获取角色
        String role;
        switch (rolePosition) {
            case 0:
                role = Constants.ROLE_NURSE;
                break;
            case 1:
                role = Constants.ROLE_ATTENDANT;
                break;
            case 2:
                role = Constants.ROLE_PATIENT;
                break;
            default:
                role = Constants.ROLE_PATIENT;
                break;
        }
        
        btnRegister.setEnabled(false);
        btnRegister.setText("注册中...");
        
        executor.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    // 检查手机号是否已存在
                    int count = database.userDao().checkPhoneExists(phone);
                    if (count > 0) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                btnRegister.setEnabled(true);
                                btnRegister.setText("注册");
                                Toast.makeText(RegisterActivity.this, "该手机号已被注册", Toast.LENGTH_SHORT).show();
                            }
                        });
                        return;
                    }
                    
                    // 加密密码
                    String encryptedPassword = CryptoUtils.encryptPassword(password);
                    
                    // 创建用户
                    User user = new User(name, role, phone, encryptedPassword);
                    long userId = database.userDao().insert(user);
                    
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            btnRegister.setEnabled(true);
                            btnRegister.setText("注册");
                            
                            if (userId > 0) {
                                Toast.makeText(RegisterActivity.this, "注册成功", Toast.LENGTH_SHORT).show();
                                finish();
                            } else {
                                Toast.makeText(RegisterActivity.this, "注册失败", Toast.LENGTH_SHORT).show();
                            }
                        }
                    });
                } catch (Exception e) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            btnRegister.setEnabled(true);
                            btnRegister.setText("注册");
                            Toast.makeText(RegisterActivity.this, "注册失败：" + e.getMessage(), Toast.LENGTH_SHORT).show();
                        }
                    });
                }
            }
        });
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }
} 