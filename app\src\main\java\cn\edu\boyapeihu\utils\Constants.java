package cn.edu.boyapeihu.utils;

public class Constants {
    
    // SharedPreferences 键名
    public static final String PREF_NAME = "CareManagementPrefs";
    public static final String PREF_USER_ID = "user_id";
    public static final String PREF_USER_NAME = "user_name";
    public static final String PREF_USER_ROLE = "user_role";
    public static final String PREF_USER_PHONE = "user_phone";
    public static final String PREF_IS_LOGGED_IN = "is_logged_in";
    
    // 用户角色常量
    public static final String ROLE_NURSE = "nurse";
    public static final String ROLE_ATTENDANT = "attendant";
    public static final String ROLE_PATIENT = "patient";
    
    // 排班状态常量
    public static final String SCHEDULE_STATUS_SCHEDULED = "scheduled";
    public static final String SCHEDULE_STATUS_COMPLETED = "completed";
    public static final String SCHEDULE_STATUS_CANCELLED = "cancelled";
    
    // 支付方式常量
    public static final String PAYMENT_METHOD_CASH = "cash";
    public static final String PAYMENT_METHOD_WECHAT = "wechat";
    public static final String PAYMENT_METHOD_ALIPAY = "alipay";
    
    // 消息状态常量
    public static final int MESSAGE_UNREAD = 0;
    public static final int MESSAGE_READ = 1;
    
    // Intent 额外数据键名
    public static final String EXTRA_USER_ID = "extra_user_id";
    public static final String EXTRA_USER_ROLE = "extra_user_role";
    public static final String EXTRA_SCHEDULE_ID = "extra_schedule_id";
    public static final String EXTRA_PAYMENT_ID = "extra_payment_id";
    public static final String EXTRA_SERVICE_RECORD_ID = "extra_service_record_id";
    public static final String EXTRA_CONVERSATION_USER_ID = "extra_conversation_user_id";
    public static final String EXTRA_CONVERSATION_USER_NAME = "extra_conversation_user_name";
    
    // 请求码
    public static final int REQUEST_CODE_LOGIN = 1001;
    public static final int REQUEST_CODE_REGISTER = 1002;
    public static final int REQUEST_CODE_EDIT_PROFILE = 1003;
    public static final int REQUEST_CODE_ADD_SCHEDULE = 1004;
    public static final int REQUEST_CODE_EDIT_SCHEDULE = 1005;
    public static final int REQUEST_CODE_ADD_PAYMENT = 1006;
    public static final int REQUEST_CODE_ADD_SERVICE_RECORD = 1007;
    public static final int REQUEST_CODE_ADD_QUALITY_MONITOR = 1008;
    public static final int REQUEST_CODE_PICK_IMAGE = 1009;
    
    // 结果码
    public static final int RESULT_CODE_SUCCESS = 2001;
    public static final int RESULT_CODE_CANCELLED = 2002;
    public static final int RESULT_CODE_ERROR = 2003;
    
    // 评分范围
    public static final int MIN_RATING = 1;
    public static final int MAX_RATING = 5;
    public static final int MIN_QUALITY_SCORE = 1;
    public static final int MAX_QUALITY_SCORE = 100;
    
    // 分页大小
    public static final int PAGE_SIZE = 20;
    
    // 文件路径
    public static final String AVATAR_DIR = "avatars";
    public static final String CACHE_DIR = "cache";
    
    // 默认值
    public static final String DEFAULT_AVATAR = "default_avatar.png";
    public static final double DEFAULT_PAYMENT_AMOUNT = 0.0;
    
    // 数据库版本
    public static final int DATABASE_VERSION = 1;
    
    // 时间相关常量
    public static final int HOURS_PER_DAY = 24;
    public static final int MINUTES_PER_HOUR = 60;
    public static final int SECONDS_PER_MINUTE = 60;
    public static final long MILLIS_PER_SECOND = 1000L;
    
    // 消息类型
    public static final String MESSAGE_TYPE_TEXT = "text";
    public static final String MESSAGE_TYPE_IMAGE = "image";
    public static final String MESSAGE_TYPE_SYSTEM = "system";
    
    // 通知相关
    public static final String NOTIFICATION_CHANNEL_ID = "care_management_channel";
    public static final String NOTIFICATION_CHANNEL_NAME = "陪护管理通知";
    public static final int NOTIFICATION_ID_MESSAGE = 3001;
    public static final int NOTIFICATION_ID_SCHEDULE = 3002;
    public static final int NOTIFICATION_ID_PAYMENT = 3003;
} 