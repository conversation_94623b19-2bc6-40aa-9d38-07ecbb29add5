package cn.edu.boyapeihu.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import cn.edu.boyapeihu.R;
import cn.edu.boyapeihu.database.AppDatabase;
import cn.edu.boyapeihu.entity.Schedule;
import cn.edu.boyapeihu.entity.User;
import cn.edu.boyapeihu.utils.DateTimeUtils;

public class ScheduleAdapter extends RecyclerView.Adapter<ScheduleAdapter.ScheduleViewHolder> {
    
    private Context context;
    private List<Schedule> schedules;
    private AppDatabase database;
    private ExecutorService executor;
    
    public ScheduleAdapter(Context context) {
        this.context = context;
        this.schedules = new ArrayList<>();
        this.database = AppDatabase.getDatabase(context);
        this.executor = Executors.newSingleThreadExecutor();
    }
    
    public void setSchedules(List<Schedule> schedules) {
        this.schedules = schedules;
        notifyDataSetChanged();
    }
    
    @NonNull
    @Override
    public ScheduleViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_schedule, parent, false);
        return new ScheduleViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull ScheduleViewHolder holder, int position) {
        Schedule schedule = schedules.get(position);
        holder.bind(schedule);
    }
    
    @Override
    public int getItemCount() {
        return schedules.size();
    }
    
    public class ScheduleViewHolder extends RecyclerView.ViewHolder {
        
        private CardView cardView;
        private TextView tvAttendantName;
        private TextView tvPatientName;
        private TextView tvTime;
        private TextView tvStatus;
        
        public ScheduleViewHolder(@NonNull View itemView) {
            super(itemView);
            cardView = itemView.findViewById(R.id.card_view);
            tvAttendantName = itemView.findViewById(R.id.tv_attendant_name);
            tvPatientName = itemView.findViewById(R.id.tv_patient_name);
            tvTime = itemView.findViewById(R.id.tv_time);
            tvStatus = itemView.findViewById(R.id.tv_status);
        }
        
        public void bind(Schedule schedule) {
            // 显示时间
            String timeText = DateTimeUtils.formatDisplayDateTime(schedule.getStart_time()) + 
                            " - " + DateTimeUtils.formatDisplayDateTime(schedule.getEnd_time());
            tvTime.setText(timeText);
            
            // 显示状态
            String statusText = getStatusText(schedule.getStatus());
            tvStatus.setText(statusText);
            setStatusColor(schedule.getStatus());
            
            // 异步获取用户姓名
            executor.execute(new Runnable() {
                @Override
                public void run() {
                    try {
                        // 获取陪护姓名
                        if (schedule.getAttendant_id() > 0) {
                            User attendant = database.userDao().getUserByIdSync(schedule.getAttendant_id());
                            final String attendantName = attendant != null ? attendant.getName() : "未知陪护";
                            ((android.app.Activity) context).runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    tvAttendantName.setText("陪护：" + attendantName);
                                }
                            });
                        }
                        
                        // 获取患者姓名
                        if (schedule.getPatient_id() > 0) {
                            User patient = database.userDao().getUserByIdSync(schedule.getPatient_id());
                            final String patientName = patient != null ? patient.getName() : "未知患者";
                            ((android.app.Activity) context).runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    tvPatientName.setText("患者：" + patientName);
                                }
                            });
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            });
            
            // 设置点击事件
            cardView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    // TODO: 打开排班详情
                }
            });
        }
        

        
        private String getStatusText(String status) {
            switch (status) {
                case "scheduled":
                    return "已安排";
                case "completed":
                    return "已完成";
                case "cancelled":
                    return "已取消";
                default:
                    return "未知状态";
            }
        }
        
        private void setStatusColor(String status) {
            int color;
            switch (status) {
                case "scheduled":
                    color = context.getResources().getColor(R.color.colorWarning);
                    break;
                case "completed":
                    color = context.getResources().getColor(R.color.colorSuccess);
                    break;
                case "cancelled":
                    color = context.getResources().getColor(R.color.colorError);
                    break;
                default:
                    color = context.getResources().getColor(R.color.textColorSecondary);
                    break;
            }
            tvStatus.setTextColor(color);
        }
    }
} 