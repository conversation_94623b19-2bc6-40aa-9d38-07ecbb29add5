package cn.edu.boyapeihu.activity;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import cn.edu.boyapeihu.R;
import cn.edu.boyapeihu.adapter.CaregiverAdapter;
import cn.edu.boyapeihu.database.AppDatabase;
import cn.edu.boyapeihu.entity.Caregiver;

public class CaregiverListActivity extends AppCompatActivity {
    
    private ImageView ivBack;
    private TextView tvTitle;
    private RecyclerView recyclerView;
    private CaregiverAdapter adapter;
    private AppDatabase database;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_caregiver_list);
        
        initViews();
        initData();
        setupClickListeners();
    }
    
    private void initViews() {
        ivBack = findViewById(R.id.iv_back);
        tvTitle = findViewById(R.id.tv_title);
        recyclerView = findViewById(R.id.recycler_view);
        
        tvTitle.setText("护理员风采");
        
        // 设置RecyclerView
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        adapter = new CaregiverAdapter(this);
        recyclerView.setAdapter(adapter);
    }
    
    private void initData() {
        database = AppDatabase.getDatabase(this);
        loadCaregivers();
    }
    
    private void loadCaregivers() {
        database.caregiverDao().getAllCaregivers().observe(this, new Observer<List<Caregiver>>() {
            @Override
            public void onChanged(List<Caregiver> caregivers) {
                if (caregivers == null || caregivers.isEmpty()) {
                    // 如果没有数据，插入测试数据
                    insertTestData();
                } else {
                    adapter.setCaregivers(caregivers);
                }
            }
        });
    }

    private void insertTestData() {
        ExecutorService executor = Executors.newSingleThreadExecutor();
        executor.execute(() -> {
            // 插入测试数据
            Caregiver caregiver1 = new Caregiver(
                "何春花", "高级护工", "高级", "温柔有耐心",
                "拥有丰富的护理经验，专业技能扎实，对待患者温柔耐心，深受患者和家属的信赖。",
                "曾在三甲医院工作5年，具有丰富的临床护理经验，擅长老年护理和康复护理。",
                "老年护理、康复护理、基础护理、生活照料",
                "护理专业大专毕业",
                "护士执业证书、高级护工证书、急救证书",
                "8", "138****8888", "<EMAIL>",
                4.8, 128, "", "available"
            );

            Caregiver caregiver2 = new Caregiver(
                "秋月", "中级护工", "中级", "细致入微,非常贴心",
                "工作认真负责，细心周到，善于与患者沟通，具有良好的职业素养。",
                "在护理行业工作3年，具有扎实的护理基础知识和实践经验。",
                "基础护理、生活照料、康复辅助、心理疏导",
                "护理专业中专毕业",
                "护工证书、急救证书",
                "3", "139****9999", "<EMAIL>",
                4.5, 86, "", "available"
            );

            Caregiver caregiver3 = new Caregiver(
                "张美丽", "高级护工", "高级", "专业技能过硬",
                "具有丰富的护理经验和专业技能，能够处理各种复杂的护理情况。",
                "在多家医院和护理机构工作过，积累了丰富的实践经验。",
                "重症护理、术后护理、老年护理、慢病管理",
                "护理专业本科毕业",
                "护士执业证书、高级护工证书、专科护理证书",
                "10", "137****7777", "<EMAIL>",
                4.9, 205, "", "busy"
            );

            Caregiver caregiver4 = new Caregiver(
                "李小芳", "初级护工", "初级", "学习能力强，态度认真",
                "虽然工作经验不多，但学习能力强，工作态度认真，深受培训老师好评。",
                "刚刚完成护理培训，正在积累实践经验。",
                "基础护理、生活照料",
                "护理专业中专毕业",
                "护工证书",
                "1", "136****6666", "<EMAIL>",
                4.2, 25, "", "available"
            );

            Caregiver caregiver5 = new Caregiver(
                "王大姐", "中级护工", "中级", "经验丰富，值得信赖",
                "在护理行业深耕多年，具有丰富的实践经验，深受患者家属信赖。",
                "从事护理工作6年，服务过各种类型的患者，经验丰富。",
                "老年护理、慢病护理、康复护理、心理疏导",
                "护理专业大专毕业",
                "护工证书、康复护理证书、心理咨询师证书",
                "6", "135****5555", "<EMAIL>",
                4.7, 156, "", "offline"
            );

            try {
                database.caregiverDao().insert(caregiver1);
                database.caregiverDao().insert(caregiver2);
                database.caregiverDao().insert(caregiver3);
                database.caregiverDao().insert(caregiver4);
                database.caregiverDao().insert(caregiver5);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }
    
    private void setupClickListeners() {
        ivBack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
        
        adapter.setOnItemClickListener(new CaregiverAdapter.OnItemClickListener() {
            @Override
            public void onItemClick(Caregiver caregiver) {
                Intent intent = new Intent(CaregiverListActivity.this, CaregiverDetailActivity.class);
                intent.putExtra("caregiver_id", caregiver.getId());
                startActivity(intent);
            }
        });
    }
}
