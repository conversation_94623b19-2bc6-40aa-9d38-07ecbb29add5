package cn.edu.boyapeihu.activity;

import android.content.Intent;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.floatingactionbutton.FloatingActionButton;

import java.util.List;

import cn.edu.boyapeihu.R;
import cn.edu.boyapeihu.adapter.ScheduleAdapter;
import cn.edu.boyapeihu.database.AppDatabase;
import cn.edu.boyapeihu.entity.Schedule;
import cn.edu.boyapeihu.utils.Constants;
import cn.edu.boyapeihu.utils.SessionManager;

public class ScheduleActivity extends AppCompatActivity {
    
    private RecyclerView recyclerView;
    private FloatingActionButton fabAdd;
    private ScheduleAdapter adapter;
    
    private AppDatabase database;
    private SessionManager sessionManager;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_schedule);
        
        initViews();
        initData();
        loadSchedules();
    }
    
    private void initViews() {
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle("排班管理");
        
        recyclerView = findViewById(R.id.recycler_view);
        fabAdd = findViewById(R.id.fab_add);
        
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        adapter = new ScheduleAdapter(this);
        recyclerView.setAdapter(adapter);
        
        fabAdd.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                addSchedule();
            }
        });
    }
    
    private void initData() {
        database = AppDatabase.getDatabase(this);
        sessionManager = new SessionManager(this);
        
        // 根据角色显示不同内容
        String userRole = sessionManager.getCurrentUserRole();
        if (Constants.ROLE_NURSE.equals(userRole)) {
            // 医护人员可以添加排班
            fabAdd.setVisibility(View.VISIBLE);
        } else {
            // 陪护和患者只能查看
            fabAdd.setVisibility(View.GONE);
        }
    }
    
    private void loadSchedules() {
        int currentUserId = sessionManager.getCurrentUserId();
        String userRole = sessionManager.getCurrentUserRole();
        
        if (Constants.ROLE_NURSE.equals(userRole)) {
            // 医护人员查看所有排班
            database.scheduleDao().getAllSchedules().observe(this, new Observer<List<Schedule>>() {
                @Override
                public void onChanged(List<Schedule> schedules) {
                    adapter.setSchedules(schedules);
                }
            });
        } else if (Constants.ROLE_ATTENDANT.equals(userRole)) {
            // 陪护人员查看自己的排班
            database.scheduleDao().getSchedulesByAttendant(currentUserId).observe(this, new Observer<List<Schedule>>() {
                @Override
                public void onChanged(List<Schedule> schedules) {
                    adapter.setSchedules(schedules);
                }
            });
        } else if (Constants.ROLE_PATIENT.equals(userRole)) {
            // 患者查看与自己相关的排班
            database.scheduleDao().getSchedulesByPatient(currentUserId).observe(this, new Observer<List<Schedule>>() {
                @Override
                public void onChanged(List<Schedule> schedules) {
                    adapter.setSchedules(schedules);
                }
            });
        }
    }
    
    private void addSchedule() {
        Intent intent = new Intent(this, AddScheduleActivity.class);
        startActivity(intent);
    }
    
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.schedule_menu, menu);
        return true;
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        
        if (id == android.R.id.home) {
            finish();
            return true;
        } else if (id == R.id.action_filter) {
            // TODO: 实现筛选功能
            Toast.makeText(this, "筛选功能开发中", Toast.LENGTH_SHORT).show();
            return true;
        }
        
        return super.onOptionsItemSelected(item);
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        // 返回时刷新列表
        loadSchedules();
    }
} 