package cn.edu.boyapeihu.activity;

import android.content.Intent;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.lifecycle.Observer;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.floatingactionbutton.FloatingActionButton;

import java.util.List;

import cn.edu.boyapeihu.R;
import cn.edu.boyapeihu.adapter.QualityMonitorAdapter;
import cn.edu.boyapeihu.database.AppDatabase;
import cn.edu.boyapeihu.entity.QualityMonitor;
import cn.edu.boyapeihu.utils.Constants;
import cn.edu.boyapeihu.utils.SessionManager;

public class QualityMonitorActivity extends AppCompatActivity {
    
    private RecyclerView recyclerView;
    private FloatingActionButton fabAdd;
    private QualityMonitorAdapter adapter;
    
    private AppDatabase database;
    private SessionManager sessionManager;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_quality_monitor);
        
        initViews();
        initData();
        loadQualityMonitors();
    }
    
    private void initViews() {
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle("质量监控");
        
        recyclerView = findViewById(R.id.recycler_view);
        fabAdd = findViewById(R.id.fab_add);
        
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        adapter = new QualityMonitorAdapter(this);
        recyclerView.setAdapter(adapter);
        
        fabAdd.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                addQualityMonitor();
            }
        });
    }
    
    private void initData() {
        database = AppDatabase.getDatabase(this);
        sessionManager = new SessionManager(this);
        
        // 根据角色显示不同内容
        String userRole = sessionManager.getCurrentUserRole();
        if (Constants.ROLE_NURSE.equals(userRole)) {
            // 医护人员可以添加质量评价
            fabAdd.setVisibility(View.VISIBLE);
        } else {
            // 其他角色只能查看（理论上只有医护能进入）
            fabAdd.setVisibility(View.GONE);
        }
    }
    
    private void loadQualityMonitors() {
        // 医护人员查看所有质量监控记录
        database.qualityMonitorDao().getAllQualityMonitors().observe(this, new Observer<List<QualityMonitor>>() {
            @Override
            public void onChanged(List<QualityMonitor> qualityMonitors) {
                adapter.setQualityMonitors(qualityMonitors);
            }
        });
    }
    
    private void addQualityMonitor() {
        Intent intent = new Intent(this, AddQualityMonitorActivity.class);
        startActivity(intent);
    }
    
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.quality_monitor_menu, menu);
        return true;
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        
        if (id == android.R.id.home) {
            finish();
            return true;
        } else if (id == R.id.action_filter) {
            // TODO: 实现筛选功能
            Toast.makeText(this, "筛选功能开发中", Toast.LENGTH_SHORT).show();
            return true;
        } else if (id == R.id.action_report) {
            // TODO: 实现质量报表功能
            Toast.makeText(this, "质量报表功能开发中", Toast.LENGTH_SHORT).show();
            return true;
        }
        
        return super.onOptionsItemSelected(item);
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        // 返回时刷新列表
        loadQualityMonitors();
    }
} 