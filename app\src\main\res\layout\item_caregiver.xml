<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="12dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    android:background="@color/white">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- 头像 -->
        <RelativeLayout
            android:layout_width="80dp"
            android:layout_height="80dp">

            <ImageView
                android:id="@+id/iv_avatar"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:src="@drawable/ic_person"
                android:background="@drawable/bg_circle_gray"
                android:padding="16dp"
                android:scaleType="centerInside" />

            <!-- 等级徽章 -->
            <TextView
                android:id="@+id/tv_level"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="高级"
                android:textSize="10sp"
                android:textColor="@color/white"
                android:background="@drawable/bg_blue_badge"
                android:paddingStart="6dp"
                android:paddingEnd="6dp"
                android:paddingTop="2dp"
                android:paddingBottom="2dp"
                android:layout_alignParentEnd="true"
                android:layout_alignParentTop="true" />

        </RelativeLayout>

        <!-- 护理员信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_marginStart="16dp">

            <!-- 姓名和状态 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tv_name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="何春花"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="@color/textColorPrimary" />

                <TextView
                    android:id="@+id/tv_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="空闲"
                    android:textSize="12sp"
                    android:textColor="@color/green"
                    android:background="@drawable/bg_status_available"
                    android:paddingStart="8dp"
                    android:paddingEnd="8dp"
                    android:paddingTop="2dp"
                    android:paddingBottom="2dp" />

            </LinearLayout>

            <!-- 职称 -->
            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="高级护工"
                android:textSize="14sp"
                android:textColor="@color/textColorSecondary"
                android:layout_marginTop="4dp" />

            <!-- 个人格言 -->
            <TextView
                android:id="@+id/tv_quote"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="温柔有耐心"
                android:textSize="12sp"
                android:textColor="@color/textColorOrange"
                android:textStyle="italic"
                android:layout_marginTop="6dp" />

            <!-- 评分 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginTop="8dp">

                <RatingBar
                    android:id="@+id/rating_bar"
                    style="?android:attr/ratingBarStyleSmall"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:numStars="5"
                    android:rating="4.8"
                    android:stepSize="0.1"
                    android:isIndicator="true" />

                <TextView
                    android:id="@+id/tv_rating"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="4.8"
                    android:textSize="12sp"
                    android:textColor="@color/textColorPrimary"
                    android:layout_marginStart="8dp" />

                <TextView
                    android:id="@+id/tv_review_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="(128条评价)"
                    android:textSize="12sp"
                    android:textColor="@color/textColorSecondary"
                    android:layout_marginStart="4dp" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
