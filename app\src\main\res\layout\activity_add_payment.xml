<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/backgroundColor">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        android:theme="@style/ThemeOverlay.AppCompat.ActionBar"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 缴费时间 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/cardBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="缴费时间"
                        android:textSize="16sp"
                        android:textColor="@color/textColorPrimary"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_time"
                        android:drawablePadding="8dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <EditText
                            android:id="@+id/et_payment_date"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="8dp"
                            android:hint="选择日期"
                            android:focusable="false"
                            android:clickable="true"
                            android:background="@drawable/bg_status_pill"
                            android:padding="12dp"
                            android:textSize="14sp" />

                        <EditText
                            android:id="@+id/et_payment_time"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            android:hint="选择时间"
                            android:focusable="false"
                            android:clickable="true"
                            android:background="@drawable/bg_status_pill"
                            android:padding="12dp"
                            android:textSize="14sp" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 缴费类型 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/cardBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="缴费类型"
                        android:textSize="16sp"
                        android:textColor="@color/textColorPrimary"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_payment"
                        android:drawablePadding="8dp" />

                    <Spinner
                        android:id="@+id/spinner_payment_type"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 缴费金额 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/cardBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="缴费金额"
                        android:textSize="16sp"
                        android:textColor="@color/textColorPrimary"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_payment"
                        android:drawablePadding="8dp" />

                    <EditText
                        android:id="@+id/et_amount"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="请输入金额（元）"
                        android:inputType="numberDecimal"
                        android:background="@drawable/bg_status_pill"
                        android:padding="12dp"
                        android:textSize="16sp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 陪护选择 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/cardBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="关联陪护（可选）"
                        android:textSize="16sp"
                        android:textColor="@color/textColorPrimary"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_person"
                        android:drawablePadding="8dp" />

                    <Spinner
                        android:id="@+id/spinner_attendant"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 备注描述 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/cardBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="备注描述（可选）"
                        android:textSize="16sp"
                        android:textColor="@color/textColorPrimary"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_message"
                        android:drawablePadding="8dp" />

                    <EditText
                        android:id="@+id/et_description"
                        android:layout_width="match_parent"
                        android:layout_height="100dp"
                        android:hint="请输入费用说明或备注..."
                        android:gravity="top|start"
                        android:background="@drawable/bg_status_pill"
                        android:padding="12dp"
                        android:textSize="14sp"
                        android:maxLines="4" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 保存按钮 -->
            <Button
                android:id="@+id/btn_save"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="确认缴费"
                android:textSize="16sp"
                android:textStyle="bold"
                android:background="@drawable/bg_status_pill"
                android:backgroundTint="?attr/colorPrimary"
                android:textColor="@android:color/white"
                android:padding="16dp"
                android:layout_marginTop="16dp" />

        </LinearLayout>

    </ScrollView>

</LinearLayout> 