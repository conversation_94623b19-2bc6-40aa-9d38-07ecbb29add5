<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:cardBackgroundColor="@color/cardBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 时间和状态 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/tv_time"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="排班时间"
                android:textSize="14sp"
                android:textColor="@color/textColorSecondary"
                android:drawableStart="@drawable/ic_time"
                android:drawablePadding="8dp" />

            <TextView
                android:id="@+id/tv_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="已安排"
                android:textSize="14sp"
                android:textStyle="bold"
                android:background="@drawable/bg_status_pill"
                android:padding="8dp" />

        </LinearLayout>

        <!-- 人员信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/tv_attendant_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="陪护：张三"
                android:textSize="16sp"
                android:textColor="@color/textColorPrimary"
                android:textStyle="bold"
                android:drawableStart="@drawable/ic_person"
                android:drawablePadding="8dp"
                android:layout_marginBottom="4dp" />

            <TextView
                android:id="@+id/tv_patient_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="患者：李四"
                android:textSize="16sp"
                android:textColor="@color/textColorPrimary"
                android:drawableStart="@drawable/ic_patient"
                android:drawablePadding="8dp" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView> 