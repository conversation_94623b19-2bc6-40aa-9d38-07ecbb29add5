<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/logo"
        android:label="@string/app_name"
        android:supportsRtl="true"
        android:theme="@style/Theme.Boyapeihu">

        <!-- 登录Activity作为启动页面 -->
        <activity
            android:name=".activity.LoginActivity"
            android:exported="true"
            android:theme="@style/Theme.Boyapeihu">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 注册Activity -->
        <activity
            android:name=".activity.RegisterActivity"
            android:exported="false"
            android:parentActivityName=".activity.LoginActivity" />

        <!-- 主Activity -->
        <activity
            android:name=".activity.MainActivity"
            android:exported="false" />

        <!-- 排班管理Activity -->
        <activity
            android:name=".activity.ScheduleActivity"
            android:exported="false"
            android:parentActivityName=".activity.MainActivity" />

        <!-- 添加排班Activity -->
        <activity
            android:name=".activity.AddScheduleActivity"
            android:exported="false"
            android:parentActivityName=".activity.ScheduleActivity" />

        <!-- 服务记录Activity -->
        <activity
            android:name=".activity.ServiceRecordActivity"
            android:exported="false"
            android:parentActivityName=".activity.MainActivity" />

        <!-- 添加服务记录Activity -->
        <activity
            android:name=".activity.AddServiceRecordActivity"
            android:exported="false"
            android:parentActivityName=".activity.ServiceRecordActivity" />

        <!-- 支付管理Activity -->
        <activity
            android:name=".activity.PaymentActivity"
            android:exported="false"
            android:parentActivityName=".activity.MainActivity" />

        <!-- 添加支付Activity -->
        <activity
            android:name=".activity.AddPaymentActivity"
            android:exported="false"
            android:parentActivityName=".activity.PaymentActivity" />

        <!-- 质量监控Activity -->
        <activity
            android:name=".activity.QualityMonitorActivity"
            android:exported="false"
            android:parentActivityName=".activity.MainActivity" />

        <!-- 添加质量监控Activity -->
        <activity
            android:name=".activity.AddQualityMonitorActivity"
            android:exported="false"
            android:parentActivityName=".activity.QualityMonitorActivity" />

        <!-- 消息Activity -->
        <activity
            android:name=".activity.MessageActivity"
            android:exported="false"
            android:parentActivityName=".activity.MainActivity" />

        <!-- 发送消息Activity -->
        <activity
            android:name=".activity.SendMessageActivity"
            android:exported="false"
            android:parentActivityName=".activity.MessageActivity" />

        <!-- 个人资料Activity -->
        <activity
            android:name=".activity.ProfileActivity"
            android:exported="false"
            android:parentActivityName=".activity.MainActivity" />

        <!-- 护理员风采列表Activity -->
        <activity
            android:name=".activity.CaregiverListActivity"
            android:exported="false"
            android:parentActivityName=".activity.MainActivity" />

        <!-- 护理员详情Activity -->
        <activity
            android:name=".activity.CaregiverDetailActivity"
            android:exported="false"
            android:parentActivityName=".activity.CaregiverListActivity" />

    </application>

</manifest>