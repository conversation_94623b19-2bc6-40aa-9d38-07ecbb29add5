package cn.edu.boyapeihu.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import java.util.List;

import cn.edu.boyapeihu.entity.User;

@Dao
public interface UserDao {
    
    @Insert
    long insert(User user);
    
    @Update
    void update(User user);
    
    @Delete
    void delete(User user);
    
    @Query("SELECT * FROM user WHERE id = :id")
    LiveData<User> getUserById(int id);
    
    @Query("SELECT * FROM user WHERE id = :id")
    User getUserByIdSync(int id);
    
    @Query("SELECT * FROM user WHERE phone = :phone AND password = :password")
    User login(String phone, String password);
    
    @Query("SELECT * FROM user WHERE phone = :phone")
    User getUserByPhone(String phone);
    
    @Query("SELECT * FROM user WHERE role = :role")
    LiveData<List<User>> getUsersByRole(String role);
    
    @Query("SELECT * FROM user")
    LiveData<List<User>> getAllUsers();
    
    @Query("SELECT * FROM user WHERE role = :role AND name LIKE '%' || :name || '%'")
    LiveData<List<User>> searchUsersByRoleAndName(String role, String name);
    
    @Query("DELETE FROM user WHERE id = :id")
    void deleteById(int id);
    
    @Query("SELECT COUNT(*) FROM user WHERE phone = :phone")
    int checkPhoneExists(String phone);
} 