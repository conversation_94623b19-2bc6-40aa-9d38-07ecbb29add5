package cn.edu.boyapeihu.utils;

import android.util.Base64;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;

public class CryptoUtils {
    
    private static final String SHA256 = "SHA-256";
    private static final String CHARSET = "UTF-8";
    
    /**
     * 生成随机盐值
     */
    public static String generateSalt() {
        SecureRandom random = new SecureRandom();
        byte[] salt = new byte[16];
        random.nextBytes(salt);
        return Base64.encodeToString(salt, Base64.DEFAULT);
    }
    
    /**
     * 使用SHA-256加密密码
     * @param password 原始密码
     * @param salt 盐值
     * @return 加密后的密码
     */
    public static String encryptPassword(String password, String salt) {
        try {
            String passwordWithSalt = password + salt;
            MessageDigest digest = MessageDigest.getInstance(SHA256);
            byte[] hash = digest.digest(passwordWithSalt.getBytes(CHARSET));
            
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            throw new RuntimeException("密码加密失败", e);
        }
    }
    
    /**
     * 简单的密码加密（不使用盐值，用于简化版本）
     * @param password 原始密码
     * @return 加密后的密码
     */
    public static String encryptPassword(String password) {
        try {
            MessageDigest digest = MessageDigest.getInstance(SHA256);
            byte[] hash = digest.digest(password.getBytes(CHARSET));
            
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            throw new RuntimeException("密码加密失败", e);
        }
    }
    
    /**
     * 验证密码
     * @param inputPassword 输入的密码
     * @param storedPassword 存储的加密密码
     * @param salt 盐值（可选）
     * @return 密码是否匹配
     */
    public static boolean verifyPassword(String inputPassword, String storedPassword, String salt) {
        if (salt != null && !salt.isEmpty()) {
            String encryptedInput = encryptPassword(inputPassword, salt);
            return encryptedInput.equals(storedPassword);
        } else {
            String encryptedInput = encryptPassword(inputPassword);
            return encryptedInput.equals(storedPassword);
        }
    }
    
    /**
     * 验证密码（不使用盐值）
     * @param inputPassword 输入的密码
     * @param storedPassword 存储的加密密码
     * @return 密码是否匹配
     */
    public static boolean verifyPassword(String inputPassword, String storedPassword) {
        return verifyPassword(inputPassword, storedPassword, null);
    }
    
    /**
     * 检查密码强度
     * @param password 密码
     * @return 密码强度级别 (0-弱, 1-中等, 2-强)
     */
    public static int checkPasswordStrength(String password) {
        if (password == null || password.length() < 6) {
            return 0; // 弱
        }
        
        int score = 0;
        
        // 长度检查
        if (password.length() >= 8) score++;
        
        // 包含数字
        if (password.matches(".*\\d.*")) score++;
        
        // 包含小写字母
        if (password.matches(".*[a-z].*")) score++;
        
        // 包含大写字母
        if (password.matches(".*[A-Z].*")) score++;
        
        // 包含特殊字符
        if (password.matches(".*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?].*")) score++;
        
        if (score <= 2) return 0; // 弱
        if (score <= 3) return 1; // 中等
        return 2; // 强
    }
} 