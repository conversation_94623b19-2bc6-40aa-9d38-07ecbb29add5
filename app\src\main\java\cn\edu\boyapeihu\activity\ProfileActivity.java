package cn.edu.boyapeihu.activity;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import cn.edu.boyapeihu.R;
import cn.edu.boyapeihu.database.AppDatabase;
import cn.edu.boyapeihu.entity.User;
import cn.edu.boyapeihu.utils.Constants;
import cn.edu.boyapeihu.utils.CryptoUtils;
import cn.edu.boyapeihu.utils.SessionManager;

public class ProfileActivity extends AppCompatActivity {
    
    private TextView tvRole, tvPhone;
    private EditText etName, etCurrentPassword, etNewPassword, etConfirmPassword;
    private Button btnUpdateName, btnUpdatePassword, btnLogout;
    
    private AppDatabase database;
    private SessionManager sessionManager;
    private ExecutorService executor;
    
    private User currentUser;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_profile);
        
        initViews();
        initData();
        loadUserInfo();
        setListeners();
    }
    
    private void initViews() {
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle("个人资料");
        
        tvRole = findViewById(R.id.tv_role);
        tvPhone = findViewById(R.id.tv_phone);
        etName = findViewById(R.id.et_name);
        etCurrentPassword = findViewById(R.id.et_current_password);
        etNewPassword = findViewById(R.id.et_new_password);
        etConfirmPassword = findViewById(R.id.et_confirm_password);
        btnUpdateName = findViewById(R.id.btn_update_name);
        btnUpdatePassword = findViewById(R.id.btn_update_password);
        btnLogout = findViewById(R.id.btn_logout);
    }
    
    private void initData() {
        database = AppDatabase.getDatabase(this);
        sessionManager = new SessionManager(this);
        executor = Executors.newSingleThreadExecutor();
    }
    
    private void loadUserInfo() {
        int currentUserId = sessionManager.getCurrentUserId();
        
        executor.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    currentUser = database.userDao().getUserByIdSync(currentUserId);
                    
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            if (currentUser != null) {
                                displayUserInfo();
                            }
                        }
                    });
                } catch (Exception e) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            Toast.makeText(ProfileActivity.this, "加载用户信息失败：" + e.getMessage(), Toast.LENGTH_SHORT).show();
                        }
                    });
                }
            }
        });
    }
    
    private void displayUserInfo() {
        etName.setText(currentUser.getName());
        tvPhone.setText(currentUser.getPhone());
        
        String roleDisplayName = getRoleDisplayName(currentUser.getRole());
        tvRole.setText(roleDisplayName);
    }
    
    private String getRoleDisplayName(String role) {
        switch (role) {
            case Constants.ROLE_NURSE:
                return "医护人员";
            case Constants.ROLE_ATTENDANT:
                return "陪护人员";
            case Constants.ROLE_PATIENT:
                return "患者";
            default:
                return "未知角色";
        }
    }
    
    private void setListeners() {
        btnUpdateName.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                updateName();
            }
        });
        
        btnUpdatePassword.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                updatePassword();
            }
        });

        btnLogout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                logout();
            }
        });
    }
    
    private void updateName() {
        String newName = etName.getText().toString().trim();
        
        if (TextUtils.isEmpty(newName)) {
            Toast.makeText(this, "请输入姓名", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (newName.equals(currentUser.getName())) {
            Toast.makeText(this, "姓名未更改", Toast.LENGTH_SHORT).show();
            return;
        }
        
        btnUpdateName.setEnabled(false);
        btnUpdateName.setText("更新中...");
        
        executor.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    currentUser.setName(newName);
                    database.userDao().update(currentUser);
                    
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            btnUpdateName.setEnabled(true);
                            btnUpdateName.setText("更新姓名");
                            
                            // 更新成功
//                            if{
                                Toast.makeText(ProfileActivity.this, "姓名更新成功", Toast.LENGTH_SHORT).show();
                                // 更新会话中的用户信息
                                sessionManager.updateUserName(newName);
//                            } else {
//                                Toast.makeText(ProfileActivity.this, "姓名更新失败", Toast.LENGTH_SHORT).show();
//                                // 恢复原来的姓名
//                                loadUserInfo();
//                            }
                        }
                    });
                } catch (Exception e) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            btnUpdateName.setEnabled(true);
                            btnUpdateName.setText("更新姓名");
                            Toast.makeText(ProfileActivity.this, "更新失败：" + e.getMessage(), Toast.LENGTH_SHORT).show();
                        }
                    });
                }
            }
        });
    }
    
    private void updatePassword() {
        String currentPassword = etCurrentPassword.getText().toString().trim();
        String newPassword = etNewPassword.getText().toString().trim();
        String confirmPassword = etConfirmPassword.getText().toString().trim();
        
        if (TextUtils.isEmpty(currentPassword)) {
            Toast.makeText(this, "请输入当前密码", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (TextUtils.isEmpty(newPassword)) {
            Toast.makeText(this, "请输入新密码", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (newPassword.length() < 6) {
            Toast.makeText(this, "新密码长度不能少于6位", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (!newPassword.equals(confirmPassword)) {
            Toast.makeText(this, "两次输入的新密码不一致", Toast.LENGTH_SHORT).show();
            return;
        }
        
        btnUpdatePassword.setEnabled(false);
        btnUpdatePassword.setText("更新中...");
        
        executor.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    // 验证当前密码
                    String encryptedCurrentPassword = CryptoUtils.encryptPassword(currentPassword);
                    if (!encryptedCurrentPassword.equals(currentUser.getPassword())) {
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                btnUpdatePassword.setEnabled(true);
                                btnUpdatePassword.setText("更新密码");
                                Toast.makeText(ProfileActivity.this, "当前密码错误", Toast.LENGTH_SHORT).show();
                            }
                        });
                        return;
                    }
                    
                    // 更新密码
                    String encryptedNewPassword = CryptoUtils.encryptPassword(newPassword);
                    currentUser.setPassword(encryptedNewPassword);
                    database.userDao().update(currentUser);
                    
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            btnUpdatePassword.setEnabled(true);
                            btnUpdatePassword.setText("更新密码");
                            
                            // 更新成功
//                            {
                                Toast.makeText(ProfileActivity.this, "密码更新成功", Toast.LENGTH_SHORT).show();
                                // 清空密码输入框
                                etCurrentPassword.setText("");
                                etNewPassword.setText("");
                                etConfirmPassword.setText("");
//                            } else {
//                                Toast.makeText(ProfileActivity.this, "密码更新失败", Toast.LENGTH_SHORT).show();
//                            }
                        }
                    });
                } catch (Exception e) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            btnUpdatePassword.setEnabled(true);
                            btnUpdatePassword.setText("更新密码");
                            Toast.makeText(ProfileActivity.this, "更新失败：" + e.getMessage(), Toast.LENGTH_SHORT).show();
                        }
                    });
                }
            }
        });
    }

    /**
     * 退出登录
     */
    private void logout() {
        // 清除会话信息
        sessionManager.logout();

        // 跳转到登录页面
        Intent intent = new Intent(this, LoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);

        // 关闭当前页面
        finish();

        Toast.makeText(this, "已退出登录", Toast.LENGTH_SHORT).show();
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }
} 