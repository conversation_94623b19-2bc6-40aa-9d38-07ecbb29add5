package cn.edu.boyapeihu.entity;

import androidx.room.Entity;
import androidx.room.PrimaryKey;
import androidx.room.ForeignKey;
import androidx.room.Ignore;

@Entity(tableName = "schedule",
        foreignKeys = {
            @ForeignKey(entity = User.class, parentColumns = "id", childColumns = "attendant_id"),
            @ForeignKey(entity = User.class, parentColumns = "id", childColumns = "patient_id"),
            @ForeignKey(entity = User.class, parentColumns = "id", childColumns = "nurse_id")
        })
public class Schedule {
    @PrimaryKey(autoGenerate = true)
    private int id;
    
    private int attendant_id;
    private int patient_id;
    private Integer nurse_id; // 可为空
    private String start_time;
    private String end_time;
    private String status; // scheduled, completed, cancelled
    
    // 构造函数
    public Schedule() {}
    
    @Ignore
    public Schedule(int attendant_id, int patient_id, String start_time, String end_time) {
        this.attendant_id = attendant_id;
        this.patient_id = patient_id;
        this.start_time = start_time;
        this.end_time = end_time;
        this.status = STATUS_SCHEDULED;
    }
    
    // Getter和Setter方法
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public int getAttendant_id() {
        return attendant_id;
    }
    
    public void setAttendant_id(int attendant_id) {
        this.attendant_id = attendant_id;
    }
    
    public int getPatient_id() {
        return patient_id;
    }
    
    public void setPatient_id(int patient_id) {
        this.patient_id = patient_id;
    }
    
    public Integer getNurse_id() {
        return nurse_id;
    }
    
    public void setNurse_id(Integer nurse_id) {
        this.nurse_id = nurse_id;
    }
    
    public String getStart_time() {
        return start_time;
    }
    
    public void setStart_time(String start_time) {
        this.start_time = start_time;
    }
    
    public String getEnd_time() {
        return end_time;
    }
    
    public void setEnd_time(String end_time) {
        this.end_time = end_time;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    // 状态常量
    public static final String STATUS_SCHEDULED = "scheduled";
    public static final String STATUS_COMPLETED = "completed";
    public static final String STATUS_CANCELLED = "cancelled";
} 