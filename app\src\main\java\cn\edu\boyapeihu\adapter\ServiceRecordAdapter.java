package cn.edu.boyapeihu.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import cn.edu.boyapeihu.R;
import cn.edu.boyapeihu.database.AppDatabase;
import cn.edu.boyapeihu.entity.ServiceRecord;
import cn.edu.boyapeihu.entity.User;
import cn.edu.boyapeihu.utils.DateTimeUtils;

public class ServiceRecordAdapter extends RecyclerView.Adapter<ServiceRecordAdapter.ServiceRecordViewHolder> {
    
    private Context context;
    private List<ServiceRecord> serviceRecords;
    private AppDatabase database;
    private ExecutorService executor;
    
    public ServiceRecordAdapter(Context context) {
        this.context = context;
        this.serviceRecords = new ArrayList<>();
        this.database = AppDatabase.getDatabase(context);
        this.executor = Executors.newSingleThreadExecutor();
    }
    
    @NonNull
    @Override
    public ServiceRecordViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_service_record, parent, false);
        return new ServiceRecordViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull ServiceRecordViewHolder holder, int position) {
        ServiceRecord serviceRecord = serviceRecords.get(position);
        holder.bind(serviceRecord);
    }
    
    @Override
    public int getItemCount() {
        return serviceRecords.size();
    }
    
    public void setServiceRecords(List<ServiceRecord> serviceRecords) {
        this.serviceRecords = serviceRecords;
        notifyDataSetChanged();
    }
    
    class ServiceRecordViewHolder extends RecyclerView.ViewHolder {
        
        private TextView tvAttendantName, tvPatientName, tvServiceTime, tvServiceType, tvServiceContent, tvRating;
        
        public ServiceRecordViewHolder(@NonNull View itemView) {
            super(itemView);
            
            tvAttendantName = itemView.findViewById(R.id.tv_attendant_name);
            tvPatientName = itemView.findViewById(R.id.tv_patient_name);
            tvServiceTime = itemView.findViewById(R.id.tv_service_time);
            tvServiceType = itemView.findViewById(R.id.tv_service_type);
            tvServiceContent = itemView.findViewById(R.id.tv_service_content);
            tvRating = itemView.findViewById(R.id.tv_rating);
        }
        
        public void bind(ServiceRecord serviceRecord) {
            // 设置服务时间
            tvServiceTime.setText(DateTimeUtils.formatDateTime(serviceRecord.getService_time()));
            
            // 设置服务类型和内容
            tvServiceType.setText(serviceRecord.getService_type());
            tvServiceContent.setText(serviceRecord.getService_content());
            
            // 设置评分
            if (serviceRecord.getRating() > 0) {
                tvRating.setText("评分: " + serviceRecord.getRating() + "/5");
                tvRating.setVisibility(View.VISIBLE);
            } else {
                tvRating.setVisibility(View.GONE);
            }
            
            // 异步加载用户名称
            loadUserNames(serviceRecord);
        }
        
        private void loadUserNames(ServiceRecord serviceRecord) {
            executor.execute(new Runnable() {
                @Override
                public void run() {
                    // 获取陪护人员姓名
                    User attendant = database.userDao().getUserByIdSync(serviceRecord.getAttendant_id());
                    // 获取患者姓名
                    User patient = database.userDao().getUserByIdSync(serviceRecord.getPatient_id());
                    
                    // 更新UI
                    if (itemView.getContext() instanceof android.app.Activity) {
                        ((android.app.Activity) itemView.getContext()).runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                if (attendant != null) {
                                    tvAttendantName.setText("陪护: " + attendant.getName());
                                }
                                if (patient != null) {
                                    tvPatientName.setText("患者: " + patient.getName());
                                }
                            }
                        });
                    }
                }
            });
        }
    }
} 