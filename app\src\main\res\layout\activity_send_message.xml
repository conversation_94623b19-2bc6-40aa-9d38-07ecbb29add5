<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/backgroundColor">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        android:theme="@style/ThemeOverlay.AppCompat.ActionBar"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 选择接收者 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/cardBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="选择接收者"
                        android:textSize="16sp"
                        android:textColor="@color/textColorPrimary"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_person"
                        android:drawablePadding="8dp" />

                    <Spinner
                        android:id="@+id/spinner_receiver"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 消息内容 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="@color/cardBackground">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="消息内容"
                        android:textSize="16sp"
                        android:textColor="@color/textColorPrimary"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp"
                        android:drawableStart="@drawable/ic_message"
                        android:drawablePadding="8dp" />

                    <EditText
                        android:id="@+id/et_message"
                        android:layout_width="match_parent"
                        android:layout_height="200dp"
                        android:hint="请输入要发送的消息内容..."
                        android:gravity="top|start"
                        android:background="@drawable/bg_status_pill"
                        android:padding="12dp"
                        android:textSize="14sp"
                        android:maxLines="10"
                        android:scrollbars="vertical" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 发送按钮 -->
            <Button
                android:id="@+id/btn_send"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="发送消息"
                android:textSize="16sp"
                android:textStyle="bold"
                android:background="@drawable/bg_status_pill"
                android:backgroundTint="?attr/colorPrimary"
                android:textColor="@android:color/white"
                android:padding="16dp"
                android:layout_marginTop="16dp" />

        </LinearLayout>

    </ScrollView>

</LinearLayout> 