package cn.edu.boyapeihu.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import java.util.List;

import cn.edu.boyapeihu.entity.Caregiver;

@Dao
public interface CaregiverDao {
    
    @Insert
    void insert(Caregiver caregiver);
    
    @Update
    void update(Caregiver caregiver);
    
    @Delete
    void delete(Caregiver caregiver);
    
    @Query("SELECT * FROM caregivers ORDER BY rating DESC")
    LiveData<List<Caregiver>> getAllCaregivers();
    
    @Query("SELECT * FROM caregivers WHERE id = :id")
    LiveData<Caregiver> getCaregiverById(int id);
    
    @Query("SELECT * FROM caregivers WHERE level = :level ORDER BY rating DESC")
    LiveData<List<Caregiver>> getCaregiversByLevel(String level);
    
    @Query("SELECT * FROM caregivers WHERE status = :status ORDER BY rating DESC")
    LiveData<List<Caregiver>> getCaregiversByStatus(String status);
    
    @Query("SELECT * FROM caregivers WHERE name LIKE '%' || :name || '%' ORDER BY rating DESC")
    LiveData<List<Caregiver>> searchCaregiversByName(String name);
    
    @Query("SELECT * FROM caregivers WHERE specialties LIKE '%' || :specialty || '%' ORDER BY rating DESC")
    LiveData<List<Caregiver>> getCaregiversBySpecialty(String specialty);
    
    @Query("SELECT COUNT(*) FROM caregivers")
    LiveData<Integer> getCaregiverCount();
    
    @Query("SELECT AVG(rating) FROM caregivers")
    LiveData<Double> getAverageRating();
}
