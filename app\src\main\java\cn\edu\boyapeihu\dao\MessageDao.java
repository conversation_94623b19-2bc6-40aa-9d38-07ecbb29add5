package cn.edu.boyapeihu.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import java.util.List;

import cn.edu.boyapeihu.entity.Message;

@Dao
public interface MessageDao {
    
    @Insert
    long insert(Message message);
    
    @Update
    void update(Message message);
    
    @Delete
    void delete(Message message);
    
    @Query("SELECT * FROM message WHERE id = :id")
    LiveData<Message> getMessageById(int id);
    
    @Query("SELECT * FROM message WHERE sender_id = :senderId ORDER BY time DESC")
    LiveData<List<Message>> getMessagesBySender(int senderId);
    
    @Query("SELECT * FROM message WHERE receiver_id = :receiverId ORDER BY time DESC")
    LiveData<List<Message>> getMessagesByReceiver(int receiverId);
    
    @Query("SELECT * FROM message WHERE (sender_id = :userId1 AND receiver_id = :userId2) OR (sender_id = :userId2 AND receiver_id = :userId1) ORDER BY time ASC")
    LiveData<List<Message>> getConversation(int userId1, int userId2);
    
    @Query("SELECT * FROM message WHERE receiver_id = :receiverId AND is_read = 0")
    LiveData<List<Message>> getUnreadMessages(int receiverId);
    
    @Query("SELECT COUNT(*) FROM message WHERE receiver_id = :receiverId AND is_read = 0")
    LiveData<Integer> getUnreadMessageCount(int receiverId);
    
    @Query("UPDATE message SET is_read = 1 WHERE id = :id")
    void markAsRead(int id);
    
    @Query("UPDATE message SET is_read = 1 WHERE receiver_id = :receiverId AND sender_id = :senderId")
    void markConversationAsRead(int receiverId, int senderId);
    
    @Query("SELECT * FROM message ORDER BY time DESC")
    LiveData<List<Message>> getAllMessages();
    
    @Query("SELECT * FROM message WHERE sender_id = :userId OR receiver_id = :userId ORDER BY time DESC")
    LiveData<List<Message>> getMessagesByUser(int userId);
    
    @Query("UPDATE message SET is_read = 1 WHERE receiver_id = :userId")
    void markAllAsRead(int userId);
    
    @Query("DELETE FROM message WHERE id = :id")
    void deleteById(int id);
    
    // 获取用户的最近联系人（基于最新消息）
    @Query("SELECT DISTINCT CASE WHEN sender_id = :userId THEN receiver_id ELSE sender_id END as contact_id " +
           "FROM message WHERE sender_id = :userId OR receiver_id = :userId " +
           "ORDER BY time DESC")
    LiveData<List<Integer>> getRecentContacts(int userId);
} 