package cn.edu.boyapeihu.activity;

import android.app.DatePickerDialog;
import android.app.TimePickerDialog;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.MenuItem;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.Spinner;
import android.widget.TimePicker;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import androidx.lifecycle.Observer;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import cn.edu.boyapeihu.R;
import cn.edu.boyapeihu.database.AppDatabase;
import cn.edu.boyapeihu.entity.ServiceRecord;
import cn.edu.boyapeihu.entity.User;
import cn.edu.boyapeihu.utils.Constants;
import cn.edu.boyapeihu.utils.DateTimeUtils;
import cn.edu.boyapeihu.utils.SessionManager;

public class AddServiceRecordActivity extends AppCompatActivity {
    
    private EditText etServiceDate, etServiceTime, etServiceContent;
    private Spinner spinnerPatient, spinnerServiceType;
    private Button btnSave;
    
    private AppDatabase database;
    private SessionManager sessionManager;
    private ExecutorService executor;
    
    private List<User> patientList;
    private ArrayAdapter<String> patientAdapter;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_add_service_record);
        
        initViews();
        initData();
        loadPatients();
        setListeners();
    }
    
    private void initViews() {
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        getSupportActionBar().setTitle("添加服务记录");
        
        etServiceDate = findViewById(R.id.et_service_date);
        etServiceTime = findViewById(R.id.et_service_time);
        etServiceContent = findViewById(R.id.et_service_content);
        spinnerPatient = findViewById(R.id.spinner_patient);
        spinnerServiceType = findViewById(R.id.spinner_service_type);
        btnSave = findViewById(R.id.btn_save);
        
        // 设置默认时间
        setDefaultDateTime();
        
        // 设置服务类型
        setupServiceTypes();
    }
    
    private void initData() {
        database = AppDatabase.getDatabase(this);
        sessionManager = new SessionManager(this);
        executor = Executors.newSingleThreadExecutor();
        
        patientList = new ArrayList<>();

        patientAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_dropdown_item, new ArrayList<String>());
        spinnerPatient.setAdapter(patientAdapter);
    }
    
    private void setDefaultDateTime() {
        String currentDate = DateTimeUtils.getCurrentDate();
        String currentTime = DateTimeUtils.getCurrentTime();
        etServiceDate.setText(currentDate);
        etServiceTime.setText(currentTime);
    }
    
    private void setupServiceTypes() {
        String[] serviceTypes = {
            "基础护理",
            "生活照料",
            "医疗协助",
            "康复辅助",
            "心理陪伴",
            "其他服务"
        };
        
        ArrayAdapter<String> serviceTypeAdapter = new ArrayAdapter<>(this, 
                android.R.layout.simple_spinner_dropdown_item, serviceTypes);
        spinnerServiceType.setAdapter(serviceTypeAdapter);
    }
    
    private void loadPatients() {
        // 加载患者列表
        database.userDao().getUsersByRole(Constants.ROLE_PATIENT).observe(this, new Observer<List<User>>() {
            @Override
            public void onChanged(List<User> users) {
                patientList.clear();
                patientAdapter.clear();
                
                for (User user : users) {
                    patientList.add(user);
                    patientAdapter.add(user.getName());
                }
                patientAdapter.notifyDataSetChanged();
            }
        });
    }
    
    private void setListeners() {
        // 服务日期选择
        etServiceDate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showDatePicker();
            }
        });
        
        // 服务时间选择
        etServiceTime.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showTimePicker();
            }
        });
        
        // 保存按钮
        btnSave.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                saveServiceRecord();
            }
        });
    }
    
    private void showDatePicker() {
        Calendar calendar = Calendar.getInstance();
        DatePickerDialog dialog = new DatePickerDialog(this,
                new DatePickerDialog.OnDateSetListener() {
                    @Override
                    public void onDateSet(DatePicker view, int year, int month, int dayOfMonth) {
                        String date = String.format("%04d-%02d-%02d", year, month + 1, dayOfMonth);
                        etServiceDate.setText(date);
                    }
                },
                calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH));
        dialog.show();
    }
    
    private void showTimePicker() {
        Calendar calendar = Calendar.getInstance();
        TimePickerDialog dialog = new TimePickerDialog(this,
                new TimePickerDialog.OnTimeSetListener() {
                    @Override
                    public void onTimeSet(TimePicker view, int hourOfDay, int minute) {
                        String time = String.format("%02d:%02d", hourOfDay, minute);
                        etServiceTime.setText(time);
                    }
                },
                calendar.get(Calendar.HOUR_OF_DAY),
                calendar.get(Calendar.MINUTE),
                true);
        dialog.show();
    }
    
    private void saveServiceRecord() {
        String serviceDate = etServiceDate.getText().toString().trim();
        String serviceTime = etServiceTime.getText().toString().trim();
        String serviceContent = etServiceContent.getText().toString().trim();
        
        if (TextUtils.isEmpty(serviceDate) || TextUtils.isEmpty(serviceTime)) {
            Toast.makeText(this, "请选择服务时间", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (spinnerPatient.getSelectedItemPosition() < 0) {
            Toast.makeText(this, "请选择患者", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (spinnerServiceType.getSelectedItemPosition() < 0) {
            Toast.makeText(this, "请选择服务类型", Toast.LENGTH_SHORT).show();
            return;
        }
        
        if (TextUtils.isEmpty(serviceContent)) {
            Toast.makeText(this, "请填写服务内容", Toast.LENGTH_SHORT).show();
            return;
        }
        
        String serviceDateTime = serviceDate + " " + serviceTime + ":00";
        User selectedPatient = patientList.get(spinnerPatient.getSelectedItemPosition());
        String selectedServiceType = (String) spinnerServiceType.getSelectedItem();
        
        btnSave.setEnabled(false);
        btnSave.setText("保存中...");
        
        executor.execute(new Runnable() {
            @Override
            public void run() {
                try {
                    ServiceRecord serviceRecord = new ServiceRecord(
                            sessionManager.getCurrentUserId(),  // 当前陪护人员ID
                            selectedPatient.getId(),             // 患者ID
                            selectedServiceType,                 // 服务类型
                            serviceDateTime,                     // 服务时间
                            serviceContent                       // 服务内容
                    );
                    
                    long result = database.serviceRecordDao().insert(serviceRecord);
                    
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            btnSave.setEnabled(true);
                            btnSave.setText("保存");
                            
                            if (result > 0) {
                                Toast.makeText(AddServiceRecordActivity.this, "服务记录保存成功", Toast.LENGTH_SHORT).show();
                                finish();
                            } else {
                                Toast.makeText(AddServiceRecordActivity.this, "服务记录保存失败", Toast.LENGTH_SHORT).show();
                            }
                        }
                    });
                } catch (Exception e) {
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            btnSave.setEnabled(true);
                            btnSave.setText("保存");
                            Toast.makeText(AddServiceRecordActivity.this, "保存失败：" + e.getMessage(), Toast.LENGTH_SHORT).show();
                        }
                    });
                }
            }
        });
    }
    
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }
} 