package cn.edu.boyapeihu.dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import java.util.List;

import cn.edu.boyapeihu.entity.QualityMonitor;

@Dao
public interface QualityMonitorDao {
    
    @Insert
    long insert(QualityMonitor qualityMonitor);
    
    @Update
    void update(QualityMonitor qualityMonitor);
    
    @Delete
    void delete(QualityMonitor qualityMonitor);
    
    @Query("SELECT * FROM quality_monitor WHERE id = :id")
    LiveData<QualityMonitor> getQualityMonitorById(int id);
    
    @Query("SELECT * FROM quality_monitor WHERE nurse_id = :nurseId ORDER BY evaluation_time DESC")
    LiveData<List<QualityMonitor>> getQualityMonitorsByNurse(int nurseId);
    
    @Query("SELECT * FROM quality_monitor WHERE attendant_id = :attendantId ORDER BY evaluation_time DESC")
    LiveData<List<QualityMonitor>> getQualityMonitorsByAttendant(int attendantId);
    
    @Query("SELECT * FROM quality_monitor WHERE patient_id = :patientId ORDER BY evaluation_time DESC")
    LiveData<List<QualityMonitor>> getQualityMonitorsByPatient(int patientId);
    
    @Query("SELECT * FROM quality_monitor ORDER BY evaluation_time DESC")
    LiveData<List<QualityMonitor>> getAllQualityMonitors();
    
    @Query("SELECT * FROM quality_monitor WHERE evaluation_time >= :startDate AND evaluation_time <= :endDate ORDER BY evaluation_time DESC")
    LiveData<List<QualityMonitor>> getQualityMonitorsByDateRange(String startDate, String endDate);
    
    @Query("SELECT AVG(quality_score) FROM quality_monitor WHERE attendant_id = :attendantId")
    LiveData<Double> getAverageScoreByAttendant(int attendantId);
    
    @Query("SELECT * FROM quality_monitor WHERE attendant_id = :attendantId AND evaluation_time >= :startDate AND evaluation_time <= :endDate ORDER BY evaluation_time DESC")
    LiveData<List<QualityMonitor>> getQualityMonitorsByAttendantAndDateRange(int attendantId, String startDate, String endDate);
    
    @Query("DELETE FROM quality_monitor WHERE id = :id")
    void deleteById(int id);
} 