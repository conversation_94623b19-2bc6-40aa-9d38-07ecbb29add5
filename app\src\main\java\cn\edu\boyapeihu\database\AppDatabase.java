package cn.edu.boyapeihu.database;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import androidx.sqlite.db.SupportSQLiteDatabase;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import cn.edu.boyapeihu.dao.CaregiverDao;
import cn.edu.boyapeihu.dao.MessageDao;
import cn.edu.boyapeihu.dao.PaymentDao;
import cn.edu.boyapeihu.dao.QualityMonitorDao;
import cn.edu.boyapeihu.dao.ScheduleDao;
import cn.edu.boyapeihu.dao.ServiceRecordDao;
import cn.edu.boyapeihu.dao.UserDao;
import cn.edu.boyapeihu.entity.Caregiver;
import cn.edu.boyapeihu.entity.Message;
import cn.edu.boyapeihu.entity.Payment;
import cn.edu.boyapeihu.entity.QualityMonitor;
import cn.edu.boyapeihu.entity.Schedule;
import cn.edu.boyapeihu.entity.ServiceRecord;
import cn.edu.boyapeihu.entity.User;
import cn.edu.boyapeihu.utils.Constants;
import cn.edu.boyapeihu.utils.CryptoUtils;

@Database(
    entities = {
        User.class,
        Schedule.class,
        Payment.class,
        ServiceRecord.class,
        QualityMonitor.class,
        Message.class,
        Caregiver.class
    },
    version = 3,
    exportSchema = false
)
public abstract class AppDatabase extends RoomDatabase {
    
    // DAO接口
    public abstract UserDao userDao();
    public abstract ScheduleDao scheduleDao();
    public abstract PaymentDao paymentDao();
    public abstract ServiceRecordDao serviceRecordDao();
    public abstract QualityMonitorDao qualityMonitorDao();
    public abstract MessageDao messageDao();
    public abstract CaregiverDao caregiverDao();
    
    // 单例模式
    private static volatile AppDatabase INSTANCE;
    private static final ExecutorService databaseWriteExecutor = Executors.newFixedThreadPool(4);

    public static AppDatabase getDatabase(Context context) {
        if (INSTANCE == null) {
            synchronized (AppDatabase.class) {
                if (INSTANCE == null) {
                    INSTANCE = Room.databaseBuilder(
                            context.getApplicationContext(),
                            AppDatabase.class,
                            "care_management_database"
                    )
                    .fallbackToDestructiveMigration()
                    .addCallback(roomDatabaseCallback)
                    .build();
                }
            }
        }
        return INSTANCE;
    }

    /**
     * 数据库创建回调，用于插入初始数据
     */
    private static RoomDatabase.Callback roomDatabaseCallback = new RoomDatabase.Callback() {
        @Override
        public void onCreate(@NonNull SupportSQLiteDatabase db) {
            super.onCreate(db);
            // 在数据库创建后插入初始数据
            databaseWriteExecutor.execute(() -> {
                if (INSTANCE != null) {
                    populateInitialData(INSTANCE.userDao(), INSTANCE.caregiverDao());
                }
            });
        }

        @Override
        public void onOpen(@NonNull SupportSQLiteDatabase db) {
            super.onOpen(db);
            // 每次打开数据库时检查并插入护理员数据
            databaseWriteExecutor.execute(() -> {
                if (INSTANCE != null) {
                    populateCaregiverData(INSTANCE.caregiverDao());
                }
            });
        }
    };

    /**
     * 插入初始测试数据
     */
    private static void populateInitialData(UserDao userDao, CaregiverDao caregiverDao) {
        try {
            // 检查是否已经有用户数据，避免重复插入
            if (userDao.checkPhoneExists("13800001001") > 0) {
                return; // 已经有初始数据，不需要重复插入
            }

            // 创建测试用户
            String defaultPassword = CryptoUtils.encryptPassword("123456");

            // 医护人员
            User nurse1 = new User("张医生", Constants.ROLE_NURSE, "13800001001", defaultPassword);
            User nurse2 = new User("李护士", Constants.ROLE_NURSE, "13800001002", defaultPassword);

            // 陪护人员
            User attendant1 = new User("王陪护", Constants.ROLE_ATTENDANT, "13800002001", defaultPassword);
            User attendant2 = new User("刘陪护", Constants.ROLE_ATTENDANT, "13800002002", defaultPassword);
            User attendant3 = new User("陈陪护", Constants.ROLE_ATTENDANT, "13800002003", defaultPassword);

            // 患者
            User patient1 = new User("赵老伯", Constants.ROLE_PATIENT, "13800003001", defaultPassword);
            User patient2 = new User("钱奶奶", Constants.ROLE_PATIENT, "13800003002", defaultPassword);
            User patient3 = new User("孙大爷", Constants.ROLE_PATIENT, "13800003003", defaultPassword);
            User patient4 = new User("李阿姨", Constants.ROLE_PATIENT, "13800003004", defaultPassword);

            // 插入用户数据
            userDao.insert(nurse1);
            userDao.insert(nurse2);
            userDao.insert(attendant1);
            userDao.insert(attendant2);
            userDao.insert(attendant3);
            userDao.insert(patient1);
            userDao.insert(patient2);
            userDao.insert(patient3);
            userDao.insert(patient4);

            // 插入护理员数据
            Caregiver caregiver1 = new Caregiver(
                "何春花", "高级护工", "高级", "温柔有耐心",
                "拥有丰富的护理经验，专业技能扎实，对待患者温柔耐心，深受患者和家属的信赖。",
                "曾在三甲医院工作5年，具有丰富的临床护理经验，擅长老年护理和康复护理。",
                "老年护理、康复护理、基础护理、生活照料",
                "护理专业大专毕业",
                "护士执业证书、高级护工证书、急救证书",
                "8", "138****8888", "<EMAIL>",
                4.8, 128, "", "available"
            );

            Caregiver caregiver2 = new Caregiver(
                "秋月", "中级护工", "中级", "细致入微,非常贴心",
                "工作认真负责，细心周到，善于与患者沟通，具有良好的职业素养。",
                "在护理行业工作3年，具有扎实的护理基础知识和实践经验。",
                "基础护理、生活照料、康复辅助、心理疏导",
                "护理专业中专毕业",
                "护工证书、急救证书",
                "3", "139****9999", "<EMAIL>",
                4.5, 86, "", "available"
            );

            Caregiver caregiver3 = new Caregiver(
                "张美丽", "高级护工", "高级", "专业技能过硬",
                "具有丰富的护理经验和专业技能，能够处理各种复杂的护理情况。",
                "在多家医院和护理机构工作过，积累了丰富的实践经验。",
                "重症护理、术后护理、老年护理、慢病管理",
                "护理专业本科毕业",
                "护士执业证书、高级护工证书、专科护理证书",
                "10", "137****7777", "<EMAIL>",
                4.9, 205, "", "busy"
            );

            Caregiver caregiver4 = new Caregiver(
                "李小芳", "初级护工", "初级", "学习能力强，态度认真",
                "虽然工作经验不多，但学习能力强，工作态度认真，深受培训老师好评。",
                "刚刚完成护理培训，正在积累实践经验。",
                "基础护理、生活照料",
                "护理专业中专毕业",
                "护工证书",
                "1", "136****6666", "<EMAIL>",
                4.2, 25, "", "available"
            );

            Caregiver caregiver5 = new Caregiver(
                "王大姐", "中级护工", "中级", "经验丰富，值得信赖",
                "在护理行业深耕多年，具有丰富的实践经验，深受患者家属信赖。",
                "从事护理工作6年，服务过各种类型的患者，经验丰富。",
                "老年护理、慢病护理、康复护理、心理疏导",
                "护理专业大专毕业",
                "护工证书、康复护理证书、心理咨询师证书",
                "6", "135****5555", "<EMAIL>",
                4.7, 156, "", "offline"
            );

            caregiverDao.insert(caregiver1);
            caregiverDao.insert(caregiver2);
            caregiverDao.insert(caregiver3);
            caregiverDao.insert(caregiver4);
            caregiverDao.insert(caregiver5);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 插入护理员数据（单独方法，用于数据库升级时调用）
     */
    private static void populateCaregiverData(CaregiverDao caregiverDao) {
        try {
            // 检查是否已经有护理员数据
            // 由于这是在后台线程中执行，我们需要同步检查
            // 这里简单地尝试插入，如果已存在会被忽略

            Caregiver caregiver1 = new Caregiver(
                "何春花", "高级护工", "高级", "温柔有耐心",
                "拥有丰富的护理经验，专业技能扎实，对待患者温柔耐心，深受患者和家属的信赖。",
                "曾在三甲医院工作5年，具有丰富的临床护理经验，擅长老年护理和康复护理。",
                "老年护理、康复护理、基础护理、生活照料",
                "护理专业大专毕业",
                "护士执业证书、高级护工证书、急救证书",
                "8", "138****8888", "<EMAIL>",
                4.8, 128, "", "available"
            );

            Caregiver caregiver2 = new Caregiver(
                "秋月", "中级护工", "中级", "细致入微,非常贴心",
                "工作认真负责，细心周到，善于与患者沟通，具有良好的职业素养。",
                "在护理行业工作3年，具有扎实的护理基础知识和实践经验。",
                "基础护理、生活照料、康复辅助、心理疏导",
                "护理专业中专毕业",
                "护工证书、急救证书",
                "3", "139****9999", "<EMAIL>",
                4.5, 86, "", "available"
            );

            Caregiver caregiver3 = new Caregiver(
                "张美丽", "高级护工", "高级", "专业技能过硬",
                "具有丰富的护理经验和专业技能，能够处理各种复杂的护理情况。",
                "在多家医院和护理机构工作过，积累了丰富的实践经验。",
                "重症护理、术后护理、老年护理、慢病管理",
                "护理专业本科毕业",
                "护士执业证书、高级护工证书、专科护理证书",
                "10", "137****7777", "<EMAIL>",
                4.9, 205, "", "busy"
            );

            Caregiver caregiver4 = new Caregiver(
                "李小芳", "初级护工", "初级", "学习能力强，态度认真",
                "虽然工作经验不多，但学习能力强，工作态度认真，深受培训老师好评。",
                "刚刚完成护理培训，正在积累实践经验。",
                "基础护理、生活照料",
                "护理专业中专毕业",
                "护工证书",
                "1", "136****6666", "<EMAIL>",
                4.2, 25, "", "available"
            );

            Caregiver caregiver5 = new Caregiver(
                "王大姐", "中级护工", "中级", "经验丰富，值得信赖",
                "在护理行业深耕多年，具有丰富的实践经验，深受患者家属信赖。",
                "从事护理工作6年，服务过各种类型的患者，经验丰富。",
                "老年护理、慢病护理、康复护理、心理疏导",
                "护理专业大专毕业",
                "护工证书、康复护理证书、心理咨询师证书",
                "6", "135****5555", "<EMAIL>",
                4.7, 156, "", "offline"
            );

            // 尝试插入数据，如果表不存在或数据已存在会被处理
            try {
                caregiverDao.insert(caregiver1);
                caregiverDao.insert(caregiver2);
                caregiverDao.insert(caregiver3);
                caregiverDao.insert(caregiver4);
                caregiverDao.insert(caregiver5);
            } catch (Exception insertException) {
                // 忽略插入异常，可能是数据已存在
                insertException.printStackTrace();
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}